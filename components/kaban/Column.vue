<script setup>
import draggable from 'vuedraggable'

defineProps({
  column: {
    type: Object,
    required: true,
  },
})
</script>

<template>
  <div class="flex-shrink-0 w-80">
    <div class="flex items-center justify-between mb-2">
      <div class="flex items-center gap-2">
        <h3 class="font-medium text-gray-300">
          {{ column.title }}
        </h3>
        <span class="text-sm text-gray-500">{{ column.items.length }}</span>
      </div>
    </div>

    <draggable
      :list="column.items"
      group="items"
      item-key="id"
      class="bg-white shadow border space-y-2 rounded-md p-2 min-h-[200px]"
    >
      <template #item="{ element }">
        <div class="bg-gray-100 p-2 rounded-md">
          {{ element.title }}
        </div>
      </template>
    </draggable>
  </div>
</template>
