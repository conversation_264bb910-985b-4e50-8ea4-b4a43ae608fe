<script lang="ts" setup>
import { useVModel } from '@vueuse/core'
import { Trash2 } from 'lucide-vue-next'
import { useUpload } from '~/composables/useUpload'

const props = defineProps<{
  modelValue: FileForm[]
  error?: string
}>()
const emit = defineEmits(['update:modelValue'])
const { uploadTemp } = useUpload()

const images = useVModel(props, 'modelValue', emit)

const loading = ref(false)
async function previewImage(files: FileList) {
  if (loading.value)
    return

  loading.value = true
  const data = await uploadTemp(files)
  loading.value = false
  if (data) {
    images.value = [...images.value, ...data.map((url: string) => ({ url, id: null, type: 'image' }))]
  }
}

function removeImage(index: number) {
  images.value.splice(index, 1)
}
</script>

<template>
  <div id="multi-images" class="col-span-1">
    <div class="xl:flex justify-between items-center">
      <div>
        Images <span class="text-sm text-gray-500">(drag to reorder)</span>
      </div>
      <FormUpload class="mt-1 xl:mt-0" @on-file-upload="previewImage" />
    </div>
    <div v-if="error" class="text-sm text-red-500">
      {{ error }}
    </div>
    <client-only>
      <div
        v-if="images.length > 0"
        class="pt-4 mt-6 custom-scrollbar"
        style="overflow-y: auto;"
      >
        <draggable v-model="images" item-key="url" class="space-y-4">
          <template #item="{ element: image, index }">
            <div
              class="relative w-fit mx-auto border border-gray-300 rounded-md p-1"
            >
              <img
                :src="fileUrl(image.url)"
                alt="Uploaded Image Preview"
                style="max-height: 200px"
              >
              <div
                class="absolute -top-2 -right-2 bg-red-500 hover:bg-red-600 transition p-1 rounded cursor-pointer flex items-center justify-center"
                @click="removeImage(index)"
              >
                <Trash2 class="w-5 h-5 text-white" />
              </div>
            </div>
          </template>
        </draggable>
      </div>
    </client-only>
  </div>
</template>
