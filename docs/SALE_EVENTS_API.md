# Sale Events API Documentation

## Overview
The Sale Events API allows management of promotional sales events, including creating, updating, and managing product associations with sale events.

**Base URL**: `/api/sale-events`
**Authentication**: Required (JWT + Staff middleware)

---

## Data Structure

### SaleEvent Object
```json
{
  "id": 1,
  "name": "Summer Sale 2024",
  "start_date": "2024-06-01",
  "end_date": "2024-08-31",
  "sale_percent": 25.50,
  "status": true,
  "description": "Great summer discounts on all products",
  "created_at": "2024-05-15T10:00:00.000000Z",
  "updated_at": "2024-05-15T10:00:00.000000Z",
  "is_active": true,
  "is_upcoming": false,
  "is_expired": false,
  "products_count": 15
}
```

### Statistics Object
```json
{
  "total_products": 15,
  "ordered_products": 8,
  "total_revenue": 2500.75,
  "can_delete": false
}
```

---

## Endpoints

### 1. List Sale Events
**GET** `/api/sale-events`

Retrieve a paginated list of sale events with optional filtering and search.

#### Query Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `search` | string | Search by sale event name |
| `status` | boolean | Filter by status (true/false) |
| `filter_type` | string | Filter by type: `current`, `upcoming`, `expired`, `active` |
| `per_page` | integer | Items per page (default: 15) |
| `page` | integer | Page number |

#### Response
```json
{
  "success": true,
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "name": "Summer Sale 2024",
        "start_date": "2024-06-01",
        "end_date": "2024-08-31",
        "sale_percent": 25.50,
        "status": true,
        "description": "Great summer discounts",
        "created_at": "2024-05-15T10:00:00.000000Z",
        "updated_at": "2024-05-15T10:00:00.000000Z",
        "is_active": true,
        "is_upcoming": false,
        "is_expired": false,
        "products_count": 15
      }
    ],
    "first_page_url": "http://example.com/api/sale-events?page=1",
    "from": 1,
    "last_page": 1,
    "last_page_url": "http://example.com/api/sale-events?page=1",
    "links": [...],
    "next_page_url": null,
    "path": "http://example.com/api/sale-events",
    "per_page": 15,
    "prev_page_url": null,
    "to": 1,
    "total": 1
  }
}
```

---

### 2. Create Sale Event
**POST** `/api/sale-events`

Create a new sale event.

#### Request Body
```json
{
  "name": "Summer Sale 2024",
  "start_date": "2024-06-01",
  "end_date": "2024-08-31",
  "sale_percent": 25.50,
  "status": true,
  "description": "Great summer discounts on all products"
}
```

#### Validation Rules
| Field | Rules | Description |
|-------|-------|-------------|
| `name` | required, string, max:255 | Sale event name |
| `start_date` | required, date, after_or_equal:today | Start date |
| `end_date` | required, date, after:start_date | End date |
| `sale_percent` | required, numeric, min:0, max:100 | Discount percentage |
| `status` | optional, boolean | Active status (default: true) |
| `description` | optional, string, max:1000 | Event description |

#### Response
```json
{
  "success": true,
  "message": "Sale event created successfully",
  "data": {
    "id": 1,
    "name": "Summer Sale 2024",
    "start_date": "2024-06-01",
    "end_date": "2024-08-31",
    "sale_percent": 25.50,
    "status": true,
    "description": "Great summer discounts on all products",
    "created_at": "2024-05-15T10:00:00.000000Z",
    "updated_at": "2024-05-15T10:00:00.000000Z",
    "is_active": false,
    "is_upcoming": true,
    "is_expired": false
  }
}
```

---

### 3. Get Sale Event Details
**GET** `/api/sale-events/{id}`

Retrieve details of a specific sale event including associated products and statistics.

#### Response
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Summer Sale 2024",
    "start_date": "2024-06-01",
    "end_date": "2024-08-31",
    "sale_percent": 25.50,
    "status": true,
    "description": "Great summer discounts",
    "created_at": "2024-05-15T10:00:00.000000Z",
    "updated_at": "2024-05-15T10:00:00.000000Z",
    "is_active": true,
    "is_upcoming": false,
    "is_expired": false,
    "products_count": 15,
    "products": [
      {
        "id": 1,
        "name": "Product 1",
        "slug": "product-1",
        "sku": "SKU001",
        "sale_id": 1
      }
    ],
    "statistics": {
      "total_products": 15,
      "ordered_products": 8,
      "total_revenue": 2500.75,
      "can_delete": false
    }
  }
}
```

---

### 4. Update Sale Event
**PUT** `/api/sale-events/{id}`

Update an existing sale event.

#### Request Body
```json
{
  "name": "Updated Summer Sale 2024",
  "start_date": "2024-06-01",
  "end_date": "2024-09-30",
  "sale_percent": 30.00,
  "status": true,
  "description": "Extended summer sale with better discounts"
}
```

#### Validation Rules
Same as create, but all fields are optional (`sometimes|required`).

#### Response
```json
{
  "success": true,
  "message": "Sale event updated successfully",
  "data": {
    "id": 1,
    "name": "Updated Summer Sale 2024",
    "start_date": "2024-06-01",
    "end_date": "2024-09-30",
    "sale_percent": 30.00,
    "status": true,
    "description": "Extended summer sale with better discounts",
    "created_at": "2024-05-15T10:00:00.000000Z",
    "updated_at": "2024-05-15T12:30:00.000000Z",
    "is_active": true,
    "is_upcoming": false,
    "is_expired": false
  }
}
```

---

### 5. Delete Sale Event
**DELETE** `/api/sale-events/{id}`

Delete a sale event. Only allowed if no products in this sale event have been ordered.

#### Response (Success)
```json
{
  "success": true,
  "message": "Sale event deleted successfully",
  "data": true
}
```

#### Response (Error - Cannot Delete)
```json
{
  "success": false,
  "message": "Cannot delete sale event because it has products that have been ordered",
  "status_code": 422
}
```

---

### 6. Toggle Sale Event Status
**PUT** `/api/sale-events/{id}/toggle-status`

Toggle the active/inactive status of a sale event.

#### Response
```json
{
  "success": true,
  "message": "Sale event activated",
  "data": {
    "id": 1,
    "name": "Summer Sale 2024",
    "status": true,
    // ... other fields
  }
}
```

## Product Management

### 8. Attach Products to Sale Event
**POST** `/api/sale-events/{id}/attach-products`

Mass attach products to a sale event.

#### Request Body
```json
{
  "product_ids": [1, 2, 3, 4, 5]
}
```

#### Validation Rules
| Field | Rules | Description |
|-------|-------|-------------|
| `product_ids` | required, array, min:1 | Array of product IDs |
| `product_ids.*` | required, integer, exists:products,id | Each product ID must exist |

#### Response
```json
{
  "success": true,
  "data": {
    "updated_count": 5,
    "message": "Successfully attached 5 products to sale event"
  }
}
```

---

### 9. Detach Products from Sale Event
**POST** `/api/sale-events/{id}/detach-products`

Mass detach products from a sale event.

#### Request Body (Specific Products)
```json
{
  "product_ids": [1, 2, 3]
}
```

#### Request Body (All Products)
```json
{}
```

#### Validation Rules
| Field | Rules | Description |
|-------|-------|-------------|
| `product_ids` | optional, array, min:1 | Array of product IDs (optional) |
| `product_ids.*` | required, integer, exists:products,id | Each product ID must exist |

#### Response (Specific Products)
```json
{
  "success": true,
  "data": {
    "updated_count": 3,
    "message": "Successfully detached 3 specific products from sale event"
  }
}
```

#### Response (All Products)
```json
{
  "success": true,
  "data": {
    "updated_count": 15,
    "message": "Successfully detached all 15 products from sale event"
  }
}
```

---

### 10. Get Sale Event Products
**GET** `/api/sale-events/{id}/products`

Get paginated list of products assigned to a specific sale event.

#### Query Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `search` | string | Search by product name, SKU, or slug |
| `per_page` | integer | Items per page (default: 20) |
| `page` | integer | Page number |

#### Response
```json
{
  "success": true,
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 1,
        "name": "Product 1",
        "slug": "product-1",
        "sku": "SKU001",
        "status": true,
        "sale_id": 1
      }
    ],
    "first_page_url": "http://example.com/api/sale-events/1/products?page=1",
    "from": 1,
    "last_page": 1,
    "last_page_url": "http://example.com/api/sale-events/1/products?page=1",
    "links": [...],
    "next_page_url": null,
    "path": "http://example.com/api/sale-events/1/products",
    "per_page": 20,
    "prev_page_url": null,
    "to": 1,
    "total": 1
  }
}
```

---

### 11. Get Available Products
**GET** `/api/sale-events/available-products/list`

Get paginated list of products available for assignment (not currently assigned to any sale event).

#### Query Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| `search` | string | Search by product name, SKU, or slug |
| `per_page` | integer | Items per page (default: 20) |
| `page` | integer | Page number |

#### Response
```json
{
  "success": true,
  "data": {
    "current_page": 1,
    "data": [
      {
        "id": 10,
        "name": "Available Product",
        "slug": "available-product",
        "sku": "SKU010",
        "status": true
      }
    ],
    "first_page_url": "http://example.com/api/sale-events/available-products/list?page=1",
    "from": 1,
    "last_page": 1,
    "last_page_url": "http://example.com/api/sale-events/available-products/list?page=1",
    "links": [...],
    "next_page_url": null,
    "path": "http://example.com/api/sale-events/available-products/list",
    "per_page": 20,
    "prev_page_url": null,
    "to": 1,
    "total": 1
  }
}
```

---

## Error Responses

### Validation Errors (422)
```json
{
  "success": false,
  "message": "The given data was invalid.",
  "errors": {
    "name": [
      "Sale event name is required."
    ],
    "sale_percent": [
      "Sale percentage must be between 0 and 100."
    ]
  },
  "status_code": 422
}
```

### Not Found (404)
```json
{
  "success": false,
  "message": "Sale event not found.",
  "status_code": 404
}
```

### Business Logic Errors (422)
```json
{
  "success": false,
  "message": "Start date cannot be after end date",
  "status_code": 422
}
```

---

## Business Rules

1. **Date Validation**: Start date must be before end date
2. **Sale Percentage**: Must be between 0 and 100 with max 2 decimal places
3. **Product Assignment**: Products can only be assigned to one sale event at a time
4. **Deletion Restrictions**: Sale events cannot be deleted if they have products that have been ordered
5. **Status Changes**: Cannot deactivate a sale event if it has products in active orders
6. **Date Restrictions**: For new events, start date must be today or later
7. **Product Availability**: Only active products without existing sale assignments can be attached

---

## Status Indicators

- **is_active**: Sale event is currently running (status=true, within date range)
- **is_upcoming**: Sale event is scheduled for future (status=true, start_date > today)
- **is_expired**: Sale event has ended (end_date < today)
- **can_delete**: Indicates if the sale event can be safely deleted
