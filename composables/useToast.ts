import { useToast as LibToast } from '@/components/ui/toast/use-toast'

export default function useToast() {
  const { toast } = LibToast()

  function notifyError(message: string, title = 'Error') {
    toast({
      title,
      description: message,
      variant: 'danger',
    })
  }

  function notifyUnexpectedError() {
    notifyError('Something went wrong!, please try again later or contact support')
  }

  function notifySuccess(message: string, title = 'Success') {
    toast({
      title,
      description: message,
      variant: 'success',
    })
  }

  function notifyInfo(message: string, title = 'Info') {
    toast({
      title,
      description: message,
      variant: 'default',
    })
  }

  return { notifyError, notifyUnexpectedError, notifySuccess, notifyInfo }
}
