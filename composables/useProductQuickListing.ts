export function useProductQuickListing(endpoint: string) {
  const loading = ref(false)
  const searchQuery = ref('')
  const searchParams = ref({
    page: 1,
    per_page: 25,
    q: '',
  })

  const { clientFetch } = useCustomFetch()

  const fetchProducts = async () => {
    loading.value = true
    const response = await clientFetch<Pagination<ProductListForCollection[]>>(
      endpoint,
      {
        params: searchParams.value,
      },
    )
    loading.value = false
    return response
  }

  function setSearchParams(params: any) {
    searchParams.value = {
      ...searchParams.value,
      ...params,
    }
    // Sync searchQuery with the search parameter
    if (params.q !== undefined) {
      searchQuery.value = params.q
    }
  }

  return {
    searchQuery,
    searchParams,
    loading,
    fetchProducts,
    setSearchParams,
  }
}
