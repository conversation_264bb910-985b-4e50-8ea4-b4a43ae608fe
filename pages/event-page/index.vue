<script lang="ts" setup>
import type { EventListItem } from '~/types/event'
import { Copy as CopyIcon, PlusCircle } from 'lucide-vue-next'

definePageMeta({
  middleware: ['auth'],
})

const tableSchema = [
  {
    key: 'title',
    label: 'Title',
  },
  {
    key: 'status',
    label: 'Status',
  },
  {
    key: 'event_link',
    label: 'Event Link',
  },
  {
    key: 'created_at',
    label: 'Created At',
  },
]

const { getDefaultHeaders, clientFetch } = useCustomFetch()
const { notifySuccess, notifyError } = useToast()
const route = useRoute()
const { setQueryParam } = useQueryParams()

// Pagination params
const routeQuery = computed(() => route.query)

// Search functionality
const searchQuery = ref(routeQuery.value.q as string || '')

// Fetch events with pagination
const { data: pagination, refresh } = await useAsyncData(
  'events',
  async () => {
    try {
      const params: any = {
        page: routeQuery.value.page,
        per_page: routeQuery.value.per_page,
        search: routeQuery.value.q,
      }

      // Only add is_active filter if it's not "all" or empty
      if (routeQuery.value.is_active && routeQuery.value.is_active !== 'all') {
        params.is_active = routeQuery.value.is_active
      }

      const { data } = await $fetch<ResponseData<Pagination<EventListItem[]>>>('/api/event-pages', {
        headers: getDefaultHeaders(),
        params,
      })
      return data
    }
    catch (error) {
      console.error('Failed to fetch events:', error)
      return null
    }
  },
  {
    watch: [routeQuery],
  },
)

const events = computed(() => pagination.value?.data || [])

// Delete event with optimistic UI update
const deletingIds = ref<number[]>([])
const optimisticEvents = computed(() => {
  if (!events.value)
    return []
  return events.value.filter(event => !deletingIds.value.includes(event.id))
})

async function doDelete(item: EventListItem) {
  // Optimistically remove from UI
  deletingIds.value.push(item.id)

  try {
    const success = await clientFetch<ResponseData<boolean>>(`/api/event-pages/${item.id}`, {
      method: 'DELETE',
    })

    if (success) {
      notifySuccess('Event deleted successfully')
      // Refresh data after successful deletion
      refresh()
    }
    else {
      // Roll back on failure
      notifyError('Failed to delete event')
      deletingIds.value = deletingIds.value.filter(id => id !== item.id)
    }
  }
  catch (error) {
    // Roll back on error
    console.error('Failed to delete event:', error)
    notifyError('Failed to delete event')
    deletingIds.value = deletingIds.value.filter(id => id !== item.id)
  }
}

// Toggle status functionality
const isUpdatingStatus = ref<number | null>(null)

async function toggleStatus(item: EventListItem) {
  isUpdatingStatus.value = item.id

  try {
    const success = await clientFetch(`/api/event-pages/${item.id}/toggle-status`, {
      method: 'PUT',
    })

    if (success) {
      item.is_active = !item.is_active
      notifySuccess('Event status updated successfully')
    }
    else {
      notifyError('Failed to update event status')
    }
  }
  catch (error) {
    console.error('Failed to update event status:', error)
    notifyError('Failed to update event status')
  }
  finally {
    isUpdatingStatus.value = null
  }
}

// Search functionality
const debouncedSearch = debounce(() => {
  setQueryParam({
    q: searchQuery.value,
    page: '1',
  })
}, 1000)

watch(() => searchQuery.value, () => {
  debouncedSearch()
})

// Format date for display
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString()
}

// Copy to clipboard functionality
async function copyToClipboard(text: string) {
  try {
    await navigator.clipboard.writeText(text)
    notifySuccess('Link copied to clipboard!')
  }
  catch (error) {
    console.error('Failed to copy to clipboard:', error)
    notifyError('Failed to copy link')
  }
}
</script>

<template>
  <div>
    <div class="flex justify-between items-center my-4">
      <NuxtLink to="/event-page/create">
        <BaseButton size="xs" class="px-4">
          <PlusCircle class="w-3 h-3" />Create
        </BaseButton>
      </NuxtLink>
    </div>

    <BaseCard>
      <BaseCardHeader>
        <div class="flex justify-between items-center">
          <BaseCardTitle>Events</BaseCardTitle>
          <div class="flex gap-4">
            <SearchOnly
              v-model="searchQuery"
              class="max-w-md"
              placeholder="Search events by title..."
            />
            <div class="w-44">
              <TableFilter
                param-key="is_active"
                label="Status"
                placeholder="All Events"
                default-value="all"
                :options="[
                  { label: 'All', value: 'all' },
                  { label: 'Active', value: '1' },
                  { label: 'Inactive', value: '0' },
                ]"
              />
            </div>
          </div>
        </div>
      </BaseCardHeader>
      <BaseCardContent>
        <TableTemplate :columns="tableSchema" :data="optimisticEvents">
          <template #title="{ item }">
            <NuxtLink :to="`/event-page/edit/${item.id}`" class="font-medium hover:underline text-primary">
              {{ item.title }}
            </NuxtLink>
          </template>

          <template #status="{ item }">
            <BaseSwitch
              :model-value="Boolean(item.is_active)"
              :disabled="isUpdatingStatus === item.id"
              @update:model-value="toggleStatus(item)"
            />
          </template>

          <template #event_link="{ item }">
            <button
              class="text-primary hover:underline cursor-pointer flex items-center gap-1"
              title="Click to copy"
              @click="copyToClipboard(`/event/${item.slug}`)"
            >
              /event/{{ item.slug }}
              <CopyIcon class="w-3 h-3" />
            </button>
          </template>

          <template #created_at="{ item }">
            {{ formatDate(item.created_at) }}
          </template>

          <template #actions="{ item }">
            <TableButtonEdit :url="`/event-page/edit/${item.id}`" />
            <TableButtonDelete @delete="() => doDelete(item)" />
          </template>
        </TableTemplate>

        <!-- Pagination -->
        <div v-if="pagination && pagination.total > 0" class="mt-6">
          <TablePagination
            :total="pagination?.total || 0"
            :current="Number(routeQuery.page || 1)"
          />
        </div>
      </BaseCardContent>
    </BaseCard>
  </div>
</template>
