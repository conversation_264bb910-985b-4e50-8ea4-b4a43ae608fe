<script lang="ts" setup>
import useProducts from '@/composables/custom/products'

definePageMeta({
  middleware: ['auth'],
})

const router = useRouter()

const { clientFetch } = useCustomFetch()
const { notifyError, notifySuccess } = useToast()
const { isSlugValid, validate, form, errors, isCheckingSlug, isCheckingSku, isSkuValid } = useProducts(toRef(true))

async function submit() {
  if (!form.price) {
    notifyError('Default price is required')
    return
  }

  if (!isSkuValid.value) {
    notifyError('SKU is not unique')
    return
  }

  if (!isSlugValid.value) {
    notifyError('Slug is not unique')
    return
  }

  if (validate()) {
    const result = await clientFetch('/api/products', {
      method: 'POST',
      body: toRaw(form),
    })

    if (result) {
      notifySuccess('Product created successfully')
      router.push('/products')
    }
  }
}
</script>

<template>
  <div>
    <div class="flex items-center justify-between mt-2">
      <div class="text-lg uppercase">
        Create Product
      </div>
      <BaseButton @click="submit">
        Save Changes
      </BaseButton>
    </div>
    <div class="grid grid-cols-12 mt-2 gap-6">
      <div class="col-span-8 space-y-6">
        <ProductsFormInfo
          v-model:form="form"
          :errors="errors"
          :is-checking-slug="isCheckingSlug"
          :is-slug-valid="isSlugValid"
          :is-checking-sku="isCheckingSku"
          :is-sku-valid="isSkuValid"
        />
        <ProductsFormOptions v-model:options="form.options" />
        <ProductsFormPrice
          v-model:location-prices="form.locationPrices"
          v-model:price="form.price"
          v-model:compare-price="form.compare_price"
        />
      </div>
      <BaseCard class="col-span-4">
        <BaseCardHeader>
          Images
        </BaseCardHeader>
        <BaseCardContent>
          <FormUploadMultiImage
            v-model="form.images"
            :error="errors.images"
          />
        </BaseCardContent>
      </BaseCard>
    </div>
  </div>
</template>
