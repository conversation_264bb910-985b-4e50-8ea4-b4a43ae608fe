<script setup lang="ts">
import type { InputProps, VModelEmits } from '@/types/component'
import { useInputLogic } from './logic'

const props = defineProps<InputProps>()
const emit = defineEmits<VModelEmits>()

const { innerValue, inputClass, style } = useInputLogic(props, emit)
</script>

<template>
  <div class="grid gap-2">
    <BaseLabel :for="id" class="text-nowrap">
      {{ label }}
    </BaseLabel>
    <div class="relative">
      <BaseInput
        :id="id"
        v-model="innerValue"
        :style="style"
        :type="type"
        :placeholder="placeholder"
        :required="required"
        :class="inputClass"
        class="max-w-[600px]"
      />
    </div>
    <p v-if="error" class="text-red-500 text-sm">
      {{ error }}
    </p>
  </div>
</template>
