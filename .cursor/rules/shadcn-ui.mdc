---
description:
globs: *.vue
alwaysApply: false
---

# Shadcn UI Components

This project uses @shadcn/ui for UI components. These are beautifully designed, accessible components that you can copy and paste into your apps.

## Finding and Using Components

Components are available in the `src/components/ui` directory, following the aliases configured in `components.json`

## Using Components

Using components from the ui directory using the configured aliases: <BaseNamedComponent></BaseNamedComponent>

Example usage:

```vue
<BaseButton variant="outline">Click me</BaseButton>

<BaseCard>
  <BaseCardHeader>
    <BaseCardTitle>Card Title</BaseCardTitle>
    <BaseCardDescription>Card Description</BaseCardDescription>
  </BaseCardHeader>
  <BaseCardContent>
    <p>Card Content</p>
  </BaseCardContent>
  <BaseCardFooter>
    <p>Card Footer</p>
  </BaseCardFooter>
</BaseCard>
```

## Installing Additional Components

Many more components are available but not currently installed. You can use context7 to gather more information about shadcn vue

To install additional components, use the Shadcn CLI:


```bash
npx shadcn-vue@latest add [component-name]
```

For example, to add the Accordion component:

```bash
npx shadcn-vue@latest add accordion
```

Some commonly used components are

- Accordion
- Alert
- AlertDialog
- AspectRatio
- Avatar
- Calendar
- Checkbox
- Collapsible
- Command
- ContextMenu
- DataTable
- DatePicker
- Dropdown Menu
- Form
- Hover Card
- Menubar
- Navigation Menu
- Popover
- Progress
- Radio Group
- ScrollArea
- Select
- Separator
- Sheet
- Skeleton
- Slider
- Switch
- Table
- Textarea
- Toast
- Toggle
- Tooltip
