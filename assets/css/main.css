/* Width of the entire scrollbar */
::-webkit-scrollbar {
  width: 15px;
  height: 15px;
}

/* Background of the scrollbar track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

/* Styling of the scrollbar thumb */
::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 10px;
  border: 3px solid #f1f1f1;
}

/* Hover state for the scrollbar thumb */
::-webkit-scrollbar-thumb:hover {
  background-color: #555;
}

.modal-container {
  @apply w-11/12 mx-auto;
  @apply sm:max-w-[540px];
  @apply md:max-w-[720px];
  @apply lg:max-w-[900px];
  @apply xl:max-w-[1140px];
  @apply 2xl:max-w-[1320px];
}
