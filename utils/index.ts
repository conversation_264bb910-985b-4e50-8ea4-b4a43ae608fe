export function fileUrl(url: string) {
  if (!url) {
    return '/placeholder.svg'
  }

  if (['http', 'https', 'data:image', 'blob:'].some(i => url.startsWith(i))) {
    return url
  }
  if (url.startsWith('/')) {
    url = url.slice(1)
  }
  if (!url.startsWith('storage/')) {
    url = `storage/${url}`
  }

  return `${useRuntimeConfig().public.imageUrl}/${url}`
}

export function getFileType(file: File): FileType | null {
  switch (file.type) {
    case 'image/jpeg':
    case 'image/png':
    case 'image/webp':
    case 'image/jpg':
      return 'image'
    case 'video/mp4':
      return 'video'
    default:
      return null
  }
}

export function debounce<T extends (...args: any[]) => void>(fn: T, delay: number) {
  let timeoutId: number

  return function (this: any, ...args: any[]) {
    clearTimeout(timeoutId)
    timeoutId = window.setTimeout(() => fn.apply(this, args), delay)
  }
}
