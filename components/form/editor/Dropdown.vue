<script setup lang="ts">
interface Option {
  label: string
  func: () => void
}

defineProps<{
  options: Option[]
  tooltipContent: string
}>()
</script>

<template>
  <BaseDropdownMenu>
    <BaseDropdownMenuTrigger>
      <BaseTooltipProvider>
        <BaseTooltip :delay-duration="0.1">
          <BaseTooltipTrigger @click="$emit('onContentClick')">
            <div class="hover:bg-gray-300 w-7 h-7 flex items-center justify-center rounded">
              <slot />
            </div>
          </BaseTooltipTrigger>
          <BaseTooltipContent>
            {{ tooltipContent }}
          </BaseTooltipContent>
        </BaseTooltip>
      </BaseTooltipProvider>
    </BaseDropdownMenuTrigger>
    <BaseDropdownMenuContent>
      <BaseDropdownMenuItem
        v-for="option in options"
        :key="option.label"
        @click="option.func"
      >
        {{ option.label }}
      </BaseDropdownMenuItem>
    </BaseDropdownMenuContent>
  </BaseDropdownMenu>
</template>
