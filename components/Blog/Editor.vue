<script setup lang="ts">
import type { BlogType } from '~/types/blog'
import Editor from '@tinymce/tinymce-vue'
import { ref } from 'vue'
import { createEmptyLocaleContent, DEFAULT_LOCALE } from '~/config/locales'

defineProps<{
  error: any
}>()

interface BlogForm {
  title: LocaleContent
  content: string
  type: BlogType
}
const form = defineModel('form', {
  type: Object as () => BlogForm,
  required: true,
})

const blogTypeOptions = [
  { label: 'Company', value: 'company' },
  { label: 'Help', value: 'help' },
  { label: 'Terms', value: 'terms' },
]

const isSaving = ref(false)

// Initialize title translations if not already set
if (!form.value.title || typeof form.value.title === 'string') {
  // Handle migration from string to LocaleContent
  const existingTitle = typeof form.value.title === 'string' ? form.value.title : ''
  const emptyContent = createEmptyLocaleContent()
  emptyContent[DEFAULT_LOCALE] = existingTitle
  form.value.title = emptyContent as LocaleContent
}
</script>

<template>
  <div class="space-y-6">
    <!-- Translatable Title using the new component -->
    <FormTranslatableInput
      v-model="form.title"
      label="Blog Title"
      :required="true"
      :disabled="isSaving"
      :error="error.title"
      placeholder="Enter a compelling blog title..."
    />

    <FormSelect
      id="blog-type"
      v-model="form.type"
      label="Blog type"
      :options="blogTypeOptions"
      :error="error.type"
      cs-placeholder="Select blog type"
      cs-class="max-w-[200px]"
    />

    <div class="space-y-2">
      <label class="block text-sm font-medium text-gray-700">Content</label>
      <div>
        <client-only>
          <Editor
            v-model="form.content"
            :api-key="useRuntimeConfig().public.tinymceApiKey"
            :init="{
              height: 500,
              menubar: true,
              plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'media', 'table', 'code', 'help', 'wordcount',
              ],
              toolbar: 'undo redo | blocks | '
                + 'bold italic forecolor | alignleft aligncenter '
                + 'alignright alignjustify | bullist numlist outdent indent | '
                + 'removeformat | help',
              content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
            }"
          />
          <template #fallback>
            <div class="p-4 bg-gray-100 text-center">
              Loading editor...
            </div>
          </template>
        </client-only>
      </div>
    </div>
  </div>
</template>
