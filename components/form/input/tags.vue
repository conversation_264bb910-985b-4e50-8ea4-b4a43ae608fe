<script setup lang="ts">
import { onClickOutside, useVModel } from '@vueuse/core'
import { getLocaleContent } from '~/config/locales'

interface Props {
  modelValue: string[]
  label?: string
  placeholder?: string
  predefinedTags?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Enter tags...',
  predefinedTags: () => [],
})

const tags = useVModel(props, 'modelValue')
const inputValue = ref('')
const showSuggestions = ref(false)

const tagRef = ref<HTMLElement | null>(null)
onClickOutside(tagRef, () => {
  showSuggestions.value = false
})

const filteredSuggestions = computed(() => {
  return props.predefinedTags.filter(tag =>
    tag.toLowerCase().includes(inputValue.value.toLowerCase())
    && !tags.value.includes(tag),
  ).slice(0, 6)
})

function selectSuggestion(tag: string) {
  tags.value.push(tag)
  inputValue.value = ''
  showSuggestions.value = false
}
</script>

<template>
  <div ref="tagRef">
    <BaseLabel v-if="label" :for="label">
      {{ label }}
    </BaseLabel>
    <div class="relative">
      <BaseTagsInput v-model="tags" class="max-w-[600px]">
        <BaseTagsInputItem v-for="item in tags" :key="item" :value="getLocaleContent(item)">
          <BaseTagsInputItemText />
          <BaseTagsInputItemDelete />
        </BaseTagsInputItem>

        <BaseTagsInputInput
          :placeholder="placeholder"
          @focus="showSuggestions = true"
          @input="$event => inputValue = $event.target.value"
        />
      </BaseTagsInput>

      <div
        v-if="showSuggestions && filteredSuggestions.length > 0"
        class="absolute z-50 w-full max-w-[600px] mt-1 bg-white border rounded-md shadow-lg"
      >
        <template v-if="filteredSuggestions.length > 0">
          <div
            v-for="suggestion in filteredSuggestions"
            :key="suggestion"
            class="px-3 py-2 cursor-pointer hover:bg-gray-100"
            @click="selectSuggestion(suggestion)"
          >
            {{ suggestion }}
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
