<script setup lang="ts">
import type { ProductListForSaleEvent } from '~/types/event'
import { Search } from 'lucide-vue-next'

interface Props {
  pagination: Pagination<ProductListForSaleEvent[]> | null
  searchQuery: string
  isLoading: boolean
  selectedProductIds: number[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'update:searchQuery', query: string): void
  (e: 'update:page', page: number): void
  (e: 'productClick', productId: number): void
}>()

// Format price utility function
function formatPrice(price: number | undefined | null, currency: string = 'VND'): string {
  if (!price && price !== 0)
    return 'N/A'

  const formatter = new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  })

  return formatter.format(price)
}

function onProductClick(productId: number) {
  emit('productClick', productId)
}

function changePage(page: number) {
  emit('update:page', page)
}

// Local search query for v-model
const localSearchQuery = ref('')

// Watch for changes in the prop and update local value
watch(() => props.searchQuery, (newValue) => {
  localSearchQuery.value = newValue
}, { immediate: true })

// Watch local search query and emit changes (debounced)
const debouncedEmitSearch = debounce((value: string) => {
  emit('update:searchQuery', value)
}, 500)

watch(localSearchQuery, (newValue) => {
  debouncedEmitSearch(newValue)
})
</script>

<template>
  <div class="space-y-4">
    <!-- Search -->
    <div class="relative">
      <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
      <BaseInput
        v-model="localSearchQuery"
        placeholder="Search by name, SKU, or slug"
        class="pl-9 w-full"
      />
    </div>

    <!-- Products List -->
    <div class="border rounded-md bg-background">
      <div v-if="isLoading" class="p-8 text-center text-muted-foreground">
        <div class="flex items-center justify-center space-x-2">
          <div class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          <span>Loading products...</span>
        </div>
      </div>
      <div v-else-if="!pagination || pagination.data.length === 0" class="p-8 text-center text-muted-foreground">
        <div class="space-y-2">
          <div class="text-3xl">
            📦
          </div>
          <p class="font-medium">
            No products found
          </p>
          <p class="text-sm">
            Try adjusting your search terms
          </p>
        </div>
      </div>
      <div v-else class="divide-y max-h-[500px] overflow-y-auto">
        <div
          v-for="product in pagination.data"
          :key="product.id"
          class="p-4 cursor-pointer transition-all duration-200 hover:bg-accent/50 group relative"
          :class="{
            'bg-primary/10 border-l-4 border-l-primary shadow-sm': selectedProductIds.includes(product.id),
            'hover:shadow-sm': !selectedProductIds.includes(product.id),
          }"
          @click="onProductClick(product.id)"
        >
          <div class="flex items-center space-x-4">
            <!-- Product Image -->
            <div class="flex-shrink-0 w-16 h-16 bg-muted rounded-lg overflow-hidden">
              <img
                v-if="product.image?.path"
                :src="fileUrl(product.image.path)"
                :alt="product.name"
                class="w-full h-full object-cover"
              >
              <div v-else class="w-full h-full flex items-center justify-center text-muted-foreground text-xs">
                No Image
              </div>
            </div>

            <!-- Product Info -->
            <div class="flex-1 min-w-0">
              <div class="flex items-start justify-between">
                <div class="flex-1 min-w-0">
                  <h4 class="font-medium text-foreground truncate group-hover:text-primary transition-colors">
                    {{ product.name }}
                  </h4>
                  <div class="flex items-center gap-4 text-sm text-muted-foreground mt-1">
                    <span>
                      SKU: <span class="font-mono">{{ product.sku }}</span>
                    </span>
                    <span v-if="product.status !== undefined" class="flex items-center gap-1">
                      <span class="w-2 h-2 rounded-full" :class="product.status ? 'bg-green-500' : 'bg-gray-400'" />
                      {{ product.status ? 'Active' : 'Inactive' }}
                    </span>
                  </div>

                  <!-- Pricing Information -->
                  <div class="mt-2 flex items-center gap-2 flex-wrap">
                    <template v-if="product.has_sale && product.sale_price">
                      <!-- Sale Price (prominent) -->
                      <span class="text-lg font-semibold text-green-600">
                        {{ formatPrice(product.sale_price, product.currency) }}
                      </span>

                      <!-- Original Price (crossed out) -->
                      <span class="text-sm text-muted-foreground line-through">
                        {{ formatPrice(product.original_price, product.currency) }}
                      </span>
                    </template>

                    <!-- Regular Price (no sale) -->
                    <template v-else-if="product.original_price">
                      <span class="text-lg font-semibold text-foreground">
                        {{ formatPrice(product.original_price, product.currency) }}
                      </span>
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Selection indicator overlay -->
          <div
            v-if="selectedProductIds.includes(product.id)"
            class="absolute inset-0 pointer-events-none"
            style="background: linear-gradient(90deg, hsl(var(--primary) / 0.05) 0%, transparent 100%)"
          />
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div v-if="pagination?.total && pagination.total > 0" class="flex items-center justify-between pt-4">
      <div class="text-sm text-muted-foreground">
        Showing <span class="font-medium">{{ pagination.data.length }}</span> of
        <span class="font-medium">{{ pagination.total }}</span> products
      </div>
      <div class="flex items-center space-x-2">
        <BaseButton
          size="sm"
          variant="outline"
          :disabled="pagination.current_page === 1"
          @click="changePage(pagination.current_page - 1)"
        >
          Previous
        </BaseButton>
        <div class="px-3 py-1 text-sm text-muted-foreground">
          Page {{ pagination.current_page }} of {{ pagination.last_page }}
        </div>
        <BaseButton
          size="sm"
          variant="outline"
          :disabled="pagination.current_page === pagination.last_page"
          @click="changePage(pagination.current_page + 1)"
        >
          Next
        </BaseButton>
      </div>
    </div>
  </div>
</template>
