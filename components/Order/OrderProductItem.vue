<script lang="ts" setup>
import type { OrderProduct } from '~/types/order'
import { formatPriceByCurrency } from '~/utils/price'

defineProps<{
  product: OrderProduct
  quantity: number
  showQuantity?: boolean
}>()
</script>

<template>
  <NuxtLink v-if="product.product" :to="`/products/edit/${product.product.id}`" class="flex items-center gap-2">
    <div v-if="showQuantity !== false && quantity > 0">
      {{ quantity }} x
    </div>
    <img :src="`${fileUrl(product.product.image.path)}`" :alt="product.product.name" class="w-16 rounded-md">
    <div class="flex flex-col">
      <div class="text-sm font-medium">
        {{ product.product.name }}
      </div>
      <div class="flex items-center gap-2">
        <template v-if="Number(product.discount_price) > 0 && product.sale_price">
          <!-- Sale Price (prominent) -->
          <span class="text-sm font-semibold text-green-600">
            {{ formatPriceByCurrency(Number(product.sale_price), product.currency) }}
          </span>

          <!-- Original Price (crossed out) -->
          <span class="text-xs text-gray-500 line-through">
            {{ formatPriceByCurrency(Number(product.price), product.currency) }}
          </span>

          <!-- Discount Badge -->
          <span v-if="product.sale_percent" class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
            -{{ product.sale_percent }}%
          </span>
        </template>

        <!-- Regular Price (no discount) -->
        <template v-else>
          <span class="text-sm text-gray-500">
            {{ formatPriceByCurrency(Number(product.price), product.currency) }}
          </span>
        </template>
      </div>
      <div class="text-sm text-gray-500 flex items-center gap-1">
        <div v-for="option in product.options" :key="option.id">
          <div v-if="option.type === 'color'">
            <div
              class="border border-gray-200 rounded-full inline-flex items-center justify-center w-4 h-4"
              :style="{ backgroundColor: option.value }"
            />
          </div>
          <div v-else class="uppercase">
            {{ option.value }}
          </div>
        </div>
      </div>
    </div>
  </NuxtLink>
</template>
