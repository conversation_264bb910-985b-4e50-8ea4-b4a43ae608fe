<script setup lang="ts">
import type { BlogType } from '~/types/blog'
import { Save } from 'lucide-vue-next'
import { createEmptyLocaleContent } from '~/config/locales'

definePageMeta({
  middleware: ['auth'],
})

const router = useRouter()
const { notifyError, notifySuccess } = useToast()
const { clientFetch } = useCustomFetch()
const { form, errors, validate } = useForm({
  title: {
    default: createEmptyLocaleContent() as LocaleContent,
    rules: [required],
  },
  content: {
    default: '',
    rules: [required],
  },
  status: {
    default: true,
    rules: [],
  },
  type: {
    default: 'company' as BlogType,
    rules: [required],
  },
})

const isSaving = ref(false)

async function onSave() {
  if (!validate()) {
    notifyError('Please fill in all required fields.')
    return
  }

  isSaving.value = true
  try {
    const success = await clientFetch('/api/blogs', {
      method: 'POST',
      body: {
        title: form.title,
        content: form.content,
        status: form.status,
        type: form.type,
      },
    })

    if (success) {
      notifySuccess('Blog created successfully')
      router.push('/blogs')
    }
  }
  catch (error) {
    notifyError('Failed to create blog')
    console.error('Error creating blog:', error)
  }
  finally {
    isSaving.value = false
  }
}
</script>

<template>
  <div class="pt-1">
    <div class="flex items-center justify-between mb-6">
      <GoBack />
      <BaseButton
        :disabled="isSaving"
        size="sm"
        @click="onSave"
      >
        <Save class="h-4 w-4" />
        {{ isSaving ? 'Saving...' : 'Save Blog' }}
      </BaseButton>
    </div>
    <BaseCard>
      <BaseCardHeader>
        <BaseCardTitle>
          New Blog
        </BaseCardTitle>
      </BaseCardHeader>
      <BaseCardContent>
        <BlogEditor v-model:form="form" class="mt-4" :error="errors" />
      </BaseCardContent>
    </BaseCard>
  </div>
</template>
