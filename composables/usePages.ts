import type { PageData, PageInput } from '~/types/page'

export function usePages() {
  const getPages = async (): Promise<PageData[]> => {
    try {
      return await $fetch('/api/pages')
    }
    catch (error) {
      console.error('Error fetching pages:', error)
      return []
    }
  }

  /**
   * Get a page by ID
   */
  const getPageById = async (id: string): Promise<PageData | null> => {
    try {
      return await $fetch(`/api/pages/${id}`)
    }
    catch (error) {
      console.error(`Error fetching page ${id}:`, error)
      return null
    }
  }

  /**
   * Save a page (create or update)
   */
  const savePage = async (pageInput: PageInput): Promise<PageData> => {
    return await $fetch('/api/pages/save', {
      method: 'POST',
      body: pageInput,
    })
  }

  /**
   * Delete a page
   */
  const deletePage = async (id: string): Promise<boolean> => {
    try {
      await $fetch(`/api/pages/${id}`, {
        method: 'DELETE',
      })
      return true
    }
    catch (error) {
      console.error(`<PERSON>rror deleting page ${id}:`, error)
      return false
    }
  }

  return {
    getPages,
    getPageById,
    savePage,
    deletePage,
  }
}
