import type { EventImageFormData, EventRowFormData, ImageAlign, ImageWidth } from '~/types/event'

export default function useEvents() {
  const { form, validate, errors, setForm } = useForm({
    title: {
      default: '',
      rules: [required],
    },
    slug: {
      default: '',
      rules: [required],
    },
    is_active: {
      default: true,
      rules: [],
    },
    event_rows: {
      default: [] as EventRowFormData[],
      rules: [],
    },
  })

  // Width and alignment options for event images
  const widthOptions = [
    { label: 'Auto', value: 'auto' as ImageWidth },
    { label: 'Full', value: 'full' as ImageWidth },
    { label: 'Half', value: 'half' as ImageWidth },
  ]

  const alignOptions = [
    { label: 'Left', value: 'left' as ImageAlign },
    { label: 'Center', value: 'center' as ImageAlign },
    { label: 'Right', value: 'right' as ImageAlign },
  ]

  // Helper function to create a new event row
  function createNewRow(): EventRowFormData {
    return {
      position: form.event_rows.length,
      event_images: [],
    }
  }

  // Helper function to create a new event image
  function createNewImage(rowIndex: number): EventImageFormData {
    const row = form.event_rows[rowIndex]
    return {
      width: 'auto',
      align: 'center',
      link_url: null,
      position: row.event_images.length,
      file: null,
    }
  }

  // Add a new row to the event
  function addRow() {
    form.event_rows.push(createNewRow())
  }

  // Remove a row from the event
  function removeRow(index: number) {
    const row = form.event_rows[index]
    if (row.id) {
      // Mark existing row for deletion
      row._destroy = true
    }
    else {
      // Remove new row directly
      form.event_rows.splice(index, 1)
      // Update positions
      form.event_rows.forEach((r, i) => {
        r.position = i
      })
    }
  }

  // Add a new image to a row
  function addImage(rowIndex: number) {
    const row = form.event_rows[rowIndex]
    if (row && !row._destroy) {
      row.event_images.push(createNewImage(rowIndex))
    }
  }

  // Remove an image from a row
  function removeImage(rowIndex: number, imageIndex: number) {
    const row = form.event_rows[rowIndex]
    const image = row.event_images[imageIndex]

    if (image.id) {
      // Mark existing image for deletion
      image._destroy = true
    }
    else {
      // Remove new image directly
      row.event_images.splice(imageIndex, 1)
      // Update positions
      row.event_images.forEach((img, i) => {
        img.position = i
      })
    }
  }

  // Move row up or down
  function moveRow(index: number, direction: 'up' | 'down') {
    const newIndex = direction === 'up' ? index - 1 : index + 1
    if (newIndex >= 0 && newIndex < form.event_rows.length) {
      const rows = [...form.event_rows]
      ;[rows[index], rows[newIndex]] = [rows[newIndex], rows[index]]

      // Update positions
      rows.forEach((row, i) => {
        row.position = i
      })

      form.event_rows = rows
    }
  }

  // Move image within a row
  function moveImage(rowIndex: number, imageIndex: number, direction: 'up' | 'down') {
    const row = form.event_rows[rowIndex]
    const newIndex = direction === 'up' ? imageIndex - 1 : imageIndex + 1

    if (newIndex >= 0 && newIndex < row.event_images.length) {
      const images = [...row.event_images]
      ;[images[imageIndex], images[newIndex]] = [images[newIndex], images[imageIndex]]

      // Update positions
      images.forEach((img, i) => {
        img.position = i
      })

      row.event_images = images
    }
  }

  // Get filtered rows (excluding destroyed ones for display)
  const visibleRows = computed(() => {
    return form.event_rows.filter(row => !row._destroy)
  })

  // Get filtered images for a row (excluding destroyed ones for display)
  function getVisibleImages(rowIndex: number) {
    const row = form.event_rows[rowIndex]
    return row?.event_images.filter(img => !img._destroy) || []
  }

  // Initialize form with default row if empty
  function initializeForm() {
    if (form.event_rows.length === 0) {
      addRow()
    }
  }

  // Generate slug from title
  function generateSlugFromTitle() {
    if (form.title) {
      form.slug = form.title
        .toLowerCase()
        .replace(/[^\w\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single
        .trim()
    }
  }

  return {
    form,
    validate,
    errors,
    setForm,
    widthOptions,
    alignOptions,
    createNewRow,
    createNewImage,
    addRow,
    removeRow,
    addImage,
    removeImage,
    moveRow,
    moveImage,
    visibleRows,
    getVisibleImages,
    initializeForm,
    generateSlugFromTitle,
  }
}
