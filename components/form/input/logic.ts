import { cn } from '@/lib/utils'
import { useVModel } from '@vueuse/core'

export function useInputLogic(props: any, emit: any) {
  const innerValue = useVModel(props, 'modelValue', emit)

  const style = computed(() => {
    return props.csWidth ? `width: ${props.csWidth}px;` : ''
  })

  const inputClass = computed(() => {
    let classes = ''
    if (props.error) {
      classes += 'border-red-500 focus-visible:ring-red-500/30'
    }
    return cn(classes, props.csClass)
  })

  return { innerValue, style, inputClass }
}
