<script setup lang="ts">
import type { Settings } from '~/types/settings'
import { Save } from 'lucide-vue-next'

const { clientFetch, getDefaultHeaders } = useCustomFetch()
const { data: settings } = await useAsyncData('settings', async () => {
  const { data } = await $fetch<ResponseData<Settings[]>>('/api/custom-settings', {
    headers: getDefaultHeaders(),
  })

  return data
})

// Track which item is currently saving
const isSaving = ref('')

// Group settings by their group field and transform to expected format
const configData = computed(() => {
  if (!settings.value)
    return {}

  const grouped: Record<string, Array<{ id: string, name: string, value: string, key: string }>> = {}

  settings.value.forEach((setting) => {
    if (!grouped[setting.group]) {
      grouped[setting.group] = []
    }

    grouped[setting.group].push({
      id: setting.key,
      name: setting.name,
      value: setting.value,
      key: setting.key,
    })
  })

  return grouped
})

// Get available tabs from the grouped data
const availableTabs = computed(() => Object.keys(configData.value))

// Default active tab - use first available tab
const activeTab = ref('')

// Set default active tab when data is loaded
watch(availableTabs, (tabs) => {
  if (tabs.length > 0 && !activeTab.value) {
    activeTab.value = tabs[0]
  }
}, { immediate: true })

// Save configuration function
async function saveConfig(config: { id: string, name: string, value: string, key: string }) {
  isSaving.value = config.id

  const success = await clientFetch(`/api/custom-settings/${config.id}`, {
    method: 'PUT',
    body: {
      value: config.value,
    },
  })

  if (success) {
    const { notifySuccess } = useToast()
    notifySuccess('Configuration saved successfully')
  }

  isSaving.value = ''
}
</script>

<template>
  <div class="container mx-auto py-10">
    <div class="mb-8">
      <h1 class="text-3xl font-bold tracking-tight">
        Configuration
      </h1>
      <p class="text-muted-foreground mt-2">
        Manage all your admin configuration settings
      </p>
    </div>

    <BaseTabs v-model="activeTab" class="w-full">
      <BaseTabsList class="mb-6">
        <BaseTabsTrigger
          v-for="tab in availableTabs"
          :key="tab"
          :value="tab"
        >
          {{ tab.charAt(0).toUpperCase() + tab.slice(1) }}
        </BaseTabsTrigger>
      </BaseTabsList>

      <template v-for="(configs, tag) in configData" :key="tag">
        <BaseTabsContent :value="tag">
          <BaseCard>
            <BaseCardHeader>
              <BaseCardTitle class="capitalize">
                {{ tag }} Settings
              </BaseCardTitle>
              <BaseCardDescription>Manage your {{ tag.toLowerCase() }} configuration settings</BaseCardDescription>
            </BaseCardHeader>
            <BaseCardContent>
              <div class="divide-y">
                <div
                  v-for="config in configs"
                  :key="config.id"
                  class="flex items-center justify-between space-x-4 py-4"
                >
                  <div class="flex-1">
                    <h4 class="text-sm font-medium leading-none mb-1">
                      {{ config.name }}
                    </h4>
                    <div class="flex items-center space-x-2">
                      <BaseInput :id="config.id" v-model="config.value" class="max-w-md" />
                    </div>
                  </div>
                  <BaseButton size="sm" :disabled="isSaving === config.id" @click="saveConfig(config)">
                    <template v-if="isSaving === config.id">
                      Saving...
                    </template>
                    <template v-else>
                      <Save class="h-4 w-4 mr-2" />
                      Save
                    </template>
                  </BaseButton>
                </div>
              </div>
            </BaseCardContent>
          </BaseCard>
        </BaseTabsContent>
      </template>
    </BaseTabs>
  </div>
</template>
