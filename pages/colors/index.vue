<script lang="ts" setup>
import { Edit, PlusCircle } from 'lucide-vue-next'

interface ColorListItem {
  id: number
  name: string
  value: string
  product_count: number
}

definePageMeta({
  middleware: ['auth'],
})

const tableSchema = [
  {
    key: 'color',
    label: 'Color',
  },
  {
    key: 'name',
    label: 'Name',
  },
  {
    key: 'value',
    label: 'Hex Value',
  },
  {
    key: 'productCount',
    label: 'Products',
  },
]

const { getDefaultHeaders, clientFetch } = useCustomFetch()
const { notifySuccess, notifyError } = useToast()

// Fetch colors
const { data: colors, refresh } = await useAsyncData(
  'colors',
  async () => {
    try {
      const { data } = await $fetch<ResponseData<ColorListItem[]>>('/api/colors', {
        headers: getDefaultHeaders(),
      })
      return data
    }
    catch (error) {
      console.error('Failed to fetch colors:', error)
      return null
    }
  },
)

// Delete color with optimistic UI update
const deletingIds = ref<number[]>([])
const optimisticColors = computed(() => {
  if (!colors.value)
    return []
  return colors.value.filter(color => !deletingIds.value.includes(color.id))
})

async function doDelete(item: ColorListItem) {
  // Optimistically remove from UI
  deletingIds.value.push(item.id)

  try {
    const success = await clientFetch<ResponseData<boolean>>(`/api/colors/${item.id}`, {
      method: 'DELETE',
    })

    if (success) {
      notifySuccess('Color deleted successfully')
      // Refresh data after successful deletion
      refresh()
    }
    else {
      // Roll back on failure
      notifyError('Failed to delete color')
      deletingIds.value = deletingIds.value.filter(id => id !== item.id)
    }
  }
  catch (error) {
    // Roll back on error
    console.error('Failed to delete color:', error)
    notifyError('Failed to delete color')
    deletingIds.value = deletingIds.value.filter(id => id !== item.id)
  }
}

// Dialog state
const dialogOpen = ref(false)
const selectedColor = ref<ColorListItem | null>(null)

// Open dialog for creating new color
function openDialog(color: ColorListItem | null) {
  selectedColor.value = color
  dialogOpen.value = true
}
// Handle dialog saved event
function handleColorSaved() {
  refresh()
}
</script>

<template>
  <div>
    <div class="flex justify-between items-center my-4">
      <h1 class="text-xl font-bold">
        Colors
      </h1>
      <BaseButton
        class="flex items-center gap-2"
        variant="outlinePrimary"
        size="sm"
        @click="openDialog(null)"
      >
        <PlusCircle class="h-4 w-4" />
        Add Color
      </BaseButton>
    </div>

    <BaseCard>
      <BaseCardContent>
        <TableTemplate :columns="tableSchema" :data="optimisticColors">
          <template #color="{ item }">
            <div class="flex items-center gap-2">
              <div
                class="w-6 h-6 rounded-full border-2 border-gray-200"
                :style="{ backgroundColor: item.value }"
              />
            </div>
          </template>

          <template #name="{ item }">
            <button
              class="font-medium hover:underline text-primary text-left"
              @click="openDialog(item)"
            >
              {{ item.name }}
            </button>
          </template>

          <template #value="{ item }">
            <code class="text-sm font-mono bg-gray-100 px-2 py-1 rounded">{{ item.value }}</code>
          </template>

          <template #productCount="{ item }">
            <div class="flex items-center">
              <span class="font-medium ml-2">{{ item.product_count || 0 }}</span>
            </div>
          </template>

          <template #actions="{ item }">
            <BaseButton
              class="p-2 rounded flex items-center"
              variant="outlinePrimary"
              size="sm"
              @click="openDialog(item)"
            >
              <Edit class="w-5 h-5" />
            </BaseButton>
            <TableButtonDelete @delete="() => doDelete(item)" />
          </template>
        </TableTemplate>
      </BaseCardContent>
    </BaseCard>

    <ColorDialog
      v-model:open="dialogOpen"
      :color="selectedColor"
      @saved="handleColorSaved"
    />
  </div>
</template>
