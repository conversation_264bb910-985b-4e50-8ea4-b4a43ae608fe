<script setup lang="ts">
import { cn } from '@/lib/utils'
import { Check, ChevronsUpDown, Search } from 'lucide-vue-next'

const props = defineProps<{
  modelValue: number | string | undefined
  options: {
    label: string
    value: string | number
  }[]
  csPlaceholder: string
  csClass?: string
}>()
const emit = defineEmits(['update:modelValue'])
const current = computed({
  get: () => props.options.find(option => option.value === props.modelValue),
  set: option => emit('update:modelValue', option?.value),
})
</script>

<template>
  <BaseCombobox v-model="current" by="label">
    <BaseComboboxAnchor class="w-full max-w-[600px]" as-child>
      <BaseComboboxTrigger as-child>
        <BaseButton variant="outline" class="justify-between">
          {{ current?.label ?? csPlaceholder }}
          <ChevronsUpDown class="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </BaseButton>
      </BaseComboboxTrigger>
    </BaseComboboxAnchor>

    <BaseComboboxList>
      <div class="relative w-full items-center">
        <BaseComboboxInput class="pl-9 focus-visible:ring-0 border-0 border-b rounded-none h-10" :placeholder="csPlaceholder" />
        <span class="absolute start-0 inset-y-0 flex items-center justify-center px-3">
          <Search class="size-4 text-muted-foreground" />
        </span>
      </div>

      <BaseComboboxEmpty>
        No data ^&gt;~&lt;^.
      </BaseComboboxEmpty>

      <BaseComboboxGroup>
        <BaseComboboxItem
          v-for="option in options"
          :key="option.value"
          :value="option"
        >
          {{ option.label }}
          <BaseComboboxItemIndicator>
            <Check :class="cn('ml-auto h-4 w-4')" />
          </BaseComboboxItemIndicator>
        </BaseComboboxItem>
      </BaseComboboxGroup>
    </BaseComboboxList>
  </BaseCombobox>
</template>
