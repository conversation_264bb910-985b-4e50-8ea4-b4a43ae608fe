# Supply Management System - Inventory Tracking

This document describes the newly implemented inventory tracking functionality for Product Variants.

## Overview

The inventory management system adds stock tracking capabilities to your existing Product Variant system. Each variant now has a `quantity` field that tracks available stock, along with comprehensive inventory management functionality.

## Database Changes

### Migration: `add_column_quantity_to_variants_table`
- **Added column**: `quantity` (unsigned integer, default 0, indexed)
- **Location**: After the `status` column in the `variants` table
- **Default value**: 0 for all existing variants

## Model Updates

### Variant Model (`app/Models/Variant.php`)

#### New Properties
- `quantity` (int) - Available stock quantity

#### New Methods
- `isInStock()` - Check if variant has any stock
- `hasStock(int $quantity)` - Check if variant has sufficient stock
- `isOutOfStock()` - Check if variant is out of stock
- `isLowStock(int $threshold = 5)` - Check if variant is low in stock

#### Example Usage
```php
$variant = Variant::find(1);

if ($variant->isInStock()) {
    echo "In stock: " . $variant->quantity;
}

if ($variant->hasStock(10)) {
    echo "Can fulfill order for 10 units";
}

if ($variant->isLowStock()) {
    echo "Low stock alert!";
}
```

## Service Layer

### VariantService (`app/Services/VariantService.php`)

#### New Inventory Management Methods

##### `updateStock(int|Variant $variant, int $quantity): Variant`
Set the exact stock quantity for a variant.

##### `increaseStock(int|Variant $variant, int $quantity): Variant`
Increase stock by a specific amount.

##### `decreaseStock(int|Variant $variant, int $quantity): Variant`
Decrease stock by a specific amount (with validation).

##### `checkStock(int|Variant $variant, int $quantity): bool`
Check if variant has sufficient stock.

##### `getLowStockVariants(int $threshold = 5): Collection`
Get all variants with low stock.

##### `getOutOfStockVariants(): Collection`
Get all out-of-stock variants.

#### Example Usage
```php
$variantService = new VariantService();

// Set stock to 100
$variant = $variantService->updateStock(1, 100);

// Increase stock by 50
$variant = $variantService->increaseStock(1, 50);

// Decrease stock by 25
$variant = $variantService->decreaseStock(1, 25);

// Check if has stock for 10 units
$hasStock = $variantService->checkStock(1, 10);

// Get low stock variants
$lowStockVariants = $variantService->getLowStockVariants(5);
```

### InventoryService (`app/Services/InventoryService.php`)

A comprehensive service for order-inventory integration.

#### Key Methods

##### `checkOrderStockAvailability(Order $order): array`
Check if all products in an order have sufficient stock.

##### `reserveStockForOrder(Order $order): bool`
Reserve stock when an order is created (optional feature).

##### `releaseStockForOrder(Order $order): bool`
Release reserved stock when an order is cancelled.

##### `getInventorySummary(): array`
Get overall inventory statistics.

## API Endpoints

### Existing Endpoints (Updated)
- `PUT /api/admin/variants/{id}` - Now accepts `quantity` parameter

### New Inventory Management Endpoints
- `PUT /api/admin/variants/{id}/stock` - Update stock quantity
- `POST /api/admin/variants/{id}/stock/increase` - Increase stock
- `POST /api/admin/variants/{id}/stock/decrease` - Decrease stock
- `GET /api/admin/variants/low-stock` - Get low stock variants
- `GET /api/admin/variants/out-of-stock` - Get out of stock variants

### Request Examples

#### Update Stock
```bash
curl -X PUT /api/admin/variants/1/stock \
  -H "Content-Type: application/json" \
  -d '{"quantity": 100}'
```

#### Increase Stock
```bash
curl -X POST /api/admin/variants/1/stock/increase \
  -H "Content-Type: application/json" \
  -d '{"quantity": 50}'
```

#### Get Low Stock Variants
```bash
curl -X GET "/api/admin/variants/low-stock?threshold=10"
```

## Request Validation

### VariantUpdateRequest
- Added `quantity` validation: `nullable|integer|min:0`

### StockUpdateRequest (New)
- `quantity`: `required|integer|min:0`

## Error Handling

The system includes comprehensive error handling:

- **Negative quantities**: Prevented at validation and service level
- **Insufficient stock**: Throws exception when trying to decrease more than available
- **Invalid variants**: Proper error messages for non-existent variants

## Integration with Orders (Optional)

The `InventoryService` provides methods to integrate with your order system:

1. **Stock Validation**: Check stock before creating orders
2. **Stock Reservation**: Reserve stock for pending orders
3. **Stock Release**: Release stock for cancelled orders

### Example Order Integration
```php
$inventoryService = new InventoryService(new VariantService());

// Check stock before creating order
$stockCheck = $inventoryService->checkOrderStockAvailability($order);
if (!$stockCheck['available']) {
    // Handle insufficient stock
    foreach ($stockCheck['issues'] as $issue) {
        echo "Issue: " . $issue['issue'] . " for " . $issue['product'];
    }
}

// Reserve stock for order (optional)
$inventoryService->reserveStockForOrder($order);
```

## Future Enhancements

Consider implementing these additional features:

1. **Stock Movement History**: Track all stock changes with timestamps
2. **Automated Reorder Points**: Set minimum stock levels with alerts
3. **Bulk Stock Updates**: Import/export stock quantities
4. **Stock Reservations**: Hold stock for pending orders
5. **Inventory Reports**: Detailed analytics and reporting
6. **Multi-location Inventory**: Track stock across multiple warehouses

## Testing

The implementation has been tested with:
- ✅ Stock updates (set, increase, decrease)
- ✅ Stock validation methods
- ✅ Error handling for edge cases
- ✅ Database migration
- ✅ API endpoint functionality

## Migration Instructions

1. Run the migration: `php artisan migrate`
2. All existing variants will have `quantity = 0`
3. Update stock quantities through the API or admin interface
4. Integrate with your frontend to display and manage inventory

## Notes

- All existing variants start with `quantity = 0` after migration
- New variants created through product options will have `quantity = 0` by default
- The system is backward compatible - existing functionality remains unchanged
- Stock quantities are tracked at the variant level, not the product level
