import type { Product } from '~/types/order'

export function formatPrice(price: number, location = 'Vietnam'): string {
  return price.toLocaleString(getLocaleString(location), { style: 'currency', currency: getCurrency(location) })
}

export function formatPriceByCurrency(price: number, currency: string): string {
  return price.toLocaleString(getLocaleStringByCurrency(currency), { style: 'currency', currency })
}

function getLocaleStringByCurrency(currency: string): string {
  return currency === 'VND' ? 'vi-VN' : 'en-US'
}

export function getCurrentPrice(product: Product, location = 'Vietnam'): number {
  return product.prices_by_location.find(price => price.location === location)?.price || product.price || 0
}

export function getLocaleString(location = 'Vietnam'): string {
  return location === 'Vietnam' ? 'vi-VN' : 'en-US'
}

export function getCurrency(location = 'Vietnam'): string {
  return location === 'Vietnam' ? 'VND' : 'USD'
}
