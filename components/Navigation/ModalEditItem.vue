<script setup lang="ts">
import type { MenuItem } from '~/types/menu'
import { required } from '@/utils/validationRules'
import { useEventBus } from '@vueuse/core'
import { Trash, X } from 'lucide-vue-next'
import { createEmptyLocaleContent, DEFAULT_LOCALE, getLocaleContent } from '~/config/locales'

const emit = defineEmits(['refresh'])
const { clientFetch } = useCustomFetch()
const { withConfirm } = useConfirm()
const collectionStore = useCollectionStore()
collectionStore.fetch()

const { form, errors, validate, setForm, reset } = useForm({
  name: {
    default: createEmptyLocaleContent() as LocaleContent,
    rules: [required],
  },
  collections: {
    default: [] as (Collection & { total: number })[],
    rules: [],
  },
})

const menuItemId = ref<number | null>(null)
const searchQuery = ref('')

// Convert title to LocaleContent format for consistency
function normalizeTitle(title: string | LocaleContent | undefined): LocaleContent {
  if (!title) {
    return createEmptyLocaleContent() as LocaleContent
  }

  if (typeof title === 'string') {
    const content = createEmptyLocaleContent()
    content[DEFAULT_LOCALE] = title
    return content as LocaleContent
  }

  return title
}

const bus = useEventBus<string>('edit-navigation-item')
bus.on((event: string, { id }: any) => {
  switch (event) {
    case 'edit-navigation-item':
      openModal()
      if (id) {
        fetchItem(id)
        menuItemId.value = id
      }
      break

    default:
      break
  }
})

async function fetchItem(id: string) {
  const data = await clientFetch<MenuItem>(`/api/menu-items/${id}`, {
    method: 'GET',
  })

  if (data) {
    setForm({
      name: normalizeTitle(data.title),
      collections: data.collections,
    })
  }
}

const isModalOpen = ref(false)

function openModal() {
  isModalOpen.value = true
}

function closeModal() {
  isModalOpen.value = false
  reset()
}

// Use composable for body scroll prevention
useModalBodyScroll(isModalOpen)

async function handleSubmit() {
  if (!validate()) {
    return
  }

  const success = await clientFetch(`/api/menu-items/${menuItemId.value}`, {
    method: 'PUT',
    body: {
      title: form.name,
      collections: form.collections,
    },
  })

  if (success) {
    emit('refresh')
    closeModal()
  }
}

function handleRemoveItem(id: number) {
  const index = form.collections.findIndex(item => item.id === id)
  form.collections.splice(index, 1)
}

function handleAddItem(collection: Collection) {
  if (!form.collections.some(item => item.id === collection.id)) {
    form.collections.push({
      ...collection,
      total: (collection as any).total || 0,
    })
  }
}

async function deleteCollection(id: number) {
  withConfirm({
    title: 'Delete Collection',
    description: 'Are you sure you want to delete this collection?',
    onConfirm: async () => {
      await deleteCollectionRequest(id)
    },
  })
}

async function deleteCollectionRequest(id: number) {
  const success = await clientFetch(`/api/collections/${id}`, {
    method: 'DELETE',
  })

  if (success) {
    await collectionStore.fetch(true)

    const index = form.collections.findIndex(item => item.id === id)
    if (index !== -1) {
      form.collections.splice(index, 1)
    }
    useToast().notifySuccess('Collection deleted successfully')
  }
}

async function deleteMenu() {
  withConfirm({
    title: 'Delete Menu Item',
    description: 'Are you sure you want to delete this menu?',
    onConfirm: async () => {
      const success = await clientFetch(`/api/menu-items/${menuItemId.value}`, {
        method: 'DELETE',
      })

      if (success) {
        useToast().notifySuccess('Menu item deleted successfully')
        closeModal()
        emit('refresh')
      }
    },
  })
}

const collectionPool = computed(() => {
  return collectionStore.pool
})

const filteredCollections = computed(() => {
  return collectionPool.value.filter((collection) => {
    const collectionName = getLocaleContent(collection.name).toLowerCase()
    return collectionName.includes(searchQuery.value.toLowerCase())
  })
})
</script>

<template>
  <div>
    <ModalWithoutTrigger
      :is-open="isModalOpen"
      title="Edit Menu Group"
      description=""
      @close="closeModal"
    >
      <div class="flex justify-end mt-4 mr-4">
        <BaseButton variant="danger" @click="deleteMenu">
          <Trash class="w-6 h-6 cursor-pointer text-white" />
          Delete Menu
        </BaseButton>
      </div>
      <div class="p-4 max-h-[calc(100vh-200px)] w-[90vw] max-w-6xl flex flex-col">
        <div class="flex items-center justify-between mb-4 flex-shrink-0">
          <div class="flex-1">
            <FormTranslatableInput
              v-model="form.name"
              label="Menu Name"
              :required="true"
              :error="errors.name"
              placeholder="Enter menu name..."
              help-text="This name will be used in the navigation menu across different languages."
            />
          </div>
        </div>
        <div class="gap-6 mt-4 flex flex-1 min-h-0">
          <div class="p-3 w-1/2 border rounded-md bg-gray-50 flex flex-col">
            <h3 class="mb-3 text-gray-700 font-medium flex-shrink-0">
              Selected Menu Items
            </h3>
            <div class="flex-1 overflow-y-auto">
              <div v-if="form.collections.length === 0" class="text-gray-400 text-sm italic">
                No items selected
              </div>
              <div v-else class="space-y-2">
                <draggable v-model="form.collections" item-key="url" class="space-y-4">
                  <template #item="{ element: item }">
                    <div class="p-3 border rounded bg-white shadow-sm">
                      <div class="flex items-center justify-between">
                        <div class="gap-2 flex items-center">
                          <span class="text-gray-600 text-sm">::</span>
                          <div class="flex flex-col">
                            <span class="font-medium">{{ getLocaleContent(item.name) }}</span>
                            <span class="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-md w-fit">
                              {{ item.total || 0 }} products
                            </span>
                          </div>
                        </div>
                        <button class="text-gray-400 hover:text-red-500" @click="handleRemoveItem(item.id)">
                          <X class="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </template>
                </draggable>
              </div>
            </div>
          </div>

          <!-- Right side - Collection pool -->
          <div class="p-3 w-1/2 border rounded-md flex flex-col">
            <SharedCollectionSearchOrCreate
              v-model:search-query="searchQuery"
              class="mb-3 flex-shrink-0"
              :exclude="form.collections"
              @create="form.collections.push({ ...($event as Collection), total: 0 })"
            />
            <div class="flex-1 overflow-y-auto">
              <div v-if="filteredCollections.length === 0" class="py-4 text-center text-gray-400 text-sm italic">
                No collections found
              </div>
              <div v-else class="space-y-2">
                <div
                  v-for="collection in filteredCollections"
                  :key="collection.id"
                  class="p-3 border rounded cursor-pointer transition-all duration-200"
                  :class="{ 'bg-blue-50 border-blue-200 hover:bg-blue-100': form.collections.some(item => item.id === collection.id),
                            'bg-white hover:bg-gray-50': !form.collections.some(item => item.id === collection.id),
                  }"
                  @click.self="handleAddItem(collection)"
                >
                  <div class="flex items-center justify-between">
                    <div
                      class="gap-2 flex items-center flex-1"
                      @click="handleAddItem(collection)"
                    >
                      <span class="text-gray-600 text-sm">::</span>
                      <div class="flex flex-col flex-1">
                        <span class="font-medium">{{ getLocaleContent(collection.name) }}</span>
                        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded-md w-fit">
                          {{ (collection as any).total || 0 }} products
                        </span>
                      </div>
                    </div>
                    <X
                      v-if="form.collections.some(item => item.id === collection.id)"
                      class="h-5 text-red-500 w-5 flex-shrink-0"
                      @click.stop="handleRemoveItem(collection.id)"
                    />
                    <Trash
                      v-else
                      class="h-4 text-red-800 w-4 flex-shrink-0"
                      @click="deleteCollection(collection.id)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="gap-4 flex justify-end">
          <BaseButton variant="ghost" @click="closeModal">
            Cancel
          </BaseButton>
          <BaseButton @click="handleSubmit">
            Save
          </BaseButton>
        </div>
      </template>
    </ModalWithoutTrigger>
  </div>
</template>
