<script setup lang="ts">
import type { Order } from '~/types/order'

import {
  ArrowLeft,
  CreditCard,
  Mail,
  MapPin,
  Package,
  Phone,
  User,
} from 'lucide-vue-next'

definePageMeta({
  middleware: ['auth'],
})

const route = useRoute()
const { notifySuccess } = useToast()

const { getDefaultHeaders, clientFetch } = useCustomFetch()

const { data: order } = await useAsyncData('order', async () => {
  const { data } = await $fetch<ResponseData<Order>>(`/api/admin/orders/${route.params.token}`, {
    headers: getDefaultHeaders(),
  })

  return data
})

async function markAsCompleted() {
  const success = await clientFetch(`/api/admin/orders/${route.params.token}/mark-as-completed`, {
    method: 'POST',
  })

  if (success) {
    order.value!.status = 'completed'
    notifySuccess('Order marked as completed')
  }
}
</script>

<template>
  <div class="min-h-screen bg-gray-50 p-4 md:p-6">
    <div v-if="order" class="mx-auto max-w-7xl">
      <!-- Header -->
      <div class="mb-6 flex items-center justify-between">
        <div class="flex items-center gap-4">
          <NuxtLink to="/orders?status=pending">
            <BaseButton variant="ghost" size="icon" class="h-8 w-8">
              <ArrowLeft class="h-4 w-4" />
            </BaseButton>
          </NuxtLink>
          <div>
            <div class="flex items-center gap-2">
              <h1 class="text-2xl font-medium text-gray-900">
                Order #{{ order.id }}
              </h1>
              <div class="text-xs uppercase font-medium border border-gray-400 rounded-md px-2 py-1">
                {{ order.payment_method }}
              </div>
              <OrderStatusBadge :status="order.status" />
            </div>
            <p class="text-sm text-gray-500">
              Placed on {{ new Date(order.created_at).toLocaleString('en-GB', {
                dateStyle: 'short',
                timeStyle: 'short',
                hour12: true,
              }) }}
            </p>
          </div>
        </div>

        <div class="flex items-center gap-2">
          <BaseButton variant="default" size="sm" @click="markAsCompleted">
            Mark as Completed
          </BaseButton>
        </div>
      </div>

      <div class="grid gap-6 lg:grid-cols-3">
        <!-- Left Column -->
        <div class="space-y-6 lg:col-span-2">
          <!-- Order Items -->
          <BaseCard>
            <BaseCardHeader class="pb-4">
              <BaseCardTitle class="flex items-center gap-2 text-lg font-medium">
                <Package class="h-5 w-5 text-gray-600" />
                Order Items
              </BaseCardTitle>
            </BaseCardHeader>
            <BaseCardContent>
              <!-- Sale Event Info -->
              <div v-if="order.order_products.some(p => p.sale_event)" class="mb-4 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                <div class="flex items-center gap-2 text-sm">
                  <span class="text-orange-700 dark:text-orange-400 font-medium">Sale Event(s) Applied:</span>
                  <span v-for="(event, index) in [...new Map(order.order_products.filter(p => p.sale_event).map(p => [p.sale_event!.id, p.sale_event])).values()]" :key="event!.id" class="inline-flex items-center">
                    <span class="text-orange-600 dark:text-orange-300">{{ event!.name }}</span>
                    <span v-if="event!.sale_percent" class="ml-1 text-xs bg-orange-200 dark:bg-orange-800 text-orange-800 dark:text-orange-200 px-1.5 py-0.5 rounded">
                      {{ event!.sale_percent }}% OFF
                    </span>
                    <span v-if="index < [...new Map(order.order_products.filter(p => p.sale_event).map(p => [p.sale_event!.id, p.sale_event])).values()].length - 1" class="mx-2 text-orange-400">,</span>
                  </span>
                </div>
              </div>

              <div class="overflow-hidden">
                <table class="w-full">
                  <thead>
                    <tr class="border-b border-gray-100">
                      <th class="pb-3 text-left text-sm font-medium text-gray-600">
                        Product
                      </th>
                      <th class="pb-3 text-right text-sm font-medium text-gray-600">
                        Unit Price
                      </th>
                      <th class="pb-3 text-center text-sm font-medium text-gray-600">
                        Qty
                      </th>
                      <th class="pb-3 text-right text-sm font-medium text-gray-600">
                        Total
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="item in order.order_products" :key="item.id" class="border-b border-gray-50">
                      <td class="py-4">
                        <OrderProductItem :product="item" :quantity="item.quantity" :show-quantity="false" />
                      </td>
                      <td class="py-4 text-right">
                        <div class="flex flex-col items-end">
                          <span v-if="item.sale_price && Number(item.discount_price) > 0" class="text-sm font-medium text-gray-900">
                            {{ formatPriceByCurrency(Number(item.sale_price), item.currency) }}
                          </span>
                          <span v-else class="text-sm font-medium text-gray-900">
                            {{ formatPriceByCurrency(Number(item.price), item.currency) }}
                          </span>
                          <span v-if="item.sale_price && Number(item.discount_price) > 0" class="text-xs text-gray-500 line-through">
                            {{ formatPriceByCurrency(Number(item.price), item.currency) }}
                          </span>
                        </div>
                      </td>
                      <td class="py-4 text-center text-sm text-gray-900">
                        {{ item.quantity }}
                      </td>
                      <td class="py-4 text-right">
                        <div class="flex flex-col items-end">
                          <span class="text-sm font-medium text-gray-900">
                            {{ formatPriceByCurrency((item.sale_price && Number(item.discount_price) > 0 ? Number(item.sale_price) : Number(item.price)) * item.quantity, item.currency) }}
                          </span>
                          <span v-if="Number(item.discount_price) > 0" class="text-xs text-green-600">
                            Save {{ formatPriceByCurrency(Number(item.discount_price) * item.quantity, item.currency) }}
                          </span>
                        </div>
                      </td>
                    </tr>
                    <tr v-if="!order.order_products || order.order_products.length === 0" class="border-b border-gray-50">
                      <td class="py-4 text-sm text-gray-500">
                        No items added
                      </td>
                      <td class="py-4" />
                      <td class="py-4" />
                      <td class="py-4" />
                    </tr>
                  </tbody>
                </table>
              </div>

              <BaseSeparator class="my-4" />

              <div class="space-y-2">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Subtotal</span>
                  <span class="font-medium">{{ formatPriceByCurrency(Number(order.total_price), order.currency) }}</span>
                </div>
                <div v-if="Number(order.discount_price) > 0" class="flex justify-between text-sm">
                  <span class="text-gray-600 flex items-center gap-2">
                    Discount
                    <span v-if="order.discount_code" class="text-xs bg-gray-100 px-1.5 py-0.5 rounded">
                      {{ order.discount_code }}
                    </span>
                  </span>
                  <span class="font-medium text-green-600">- {{ formatPriceByCurrency(Number(order.discount_price), order.currency) }}</span>
                </div>
                <BaseSeparator class="my-2" />
                <div class="flex justify-between text-base font-semibold">
                  <span>Total</span>
                  <span>{{ formatPriceByCurrency(Number(order.total_price) - Number(order.discount_price || '0'), order.currency) }}</span>
                </div>
              </div>
            </BaseCardContent>
          </BaseCard>

          <!-- Order Information -->
          <BaseCard>
            <BaseCardHeader class="pb-4">
              <BaseCardTitle class="text-lg font-medium">
                Order Information
              </BaseCardTitle>
            </BaseCardHeader>
            <BaseCardContent>
              <div class="grid gap-4 sm:grid-cols-2">
                <div>
                  <label class="text-sm font-medium text-gray-600">Order ID</label>
                  <p class="mt-1 text-sm text-gray-900">
                    {{ order.id }}
                  </p>
                </div>
                <div>
                  <label class="text-sm font-medium text-gray-600">Status</label>
                  <p class="mt-1">
                    <BaseBadge variant="secondary" class="text-xs capitalize">
                      {{ order.status }}
                    </BaseBadge>
                  </p>
                </div>
                <div>
                  <label class="text-sm font-medium text-gray-600">Payment Method</label>
                  <p class="mt-1 text-sm text-gray-900">
                    {{ order.payment_method || 'N/A' }}
                  </p>
                </div>
                <div>
                  <label class="text-sm font-medium text-gray-600">Currency</label>
                  <p class="mt-1 text-sm text-gray-900">
                    {{ order.currency || 'USD' }}
                  </p>
                </div>
              </div>
            </BaseCardContent>
          </BaseCard>
        </div>

        <!-- Right Column -->
        <div class="space-y-6">
          <!-- Customer -->
          <BaseCard>
            <BaseCardHeader class="pb-4">
              <BaseCardTitle class="flex items-center gap-2 text-lg font-medium">
                <User class="h-5 w-5 text-gray-600" />
                Customer
              </BaseCardTitle>
            </BaseCardHeader>
            <BaseCardContent>
              <div class="space-y-3">
                <div>
                  <h3 class="font-medium text-gray-900">
                    {{ order.customer?.first_name }} {{ order.customer?.last_name }}
                  </h3>
                  <p class="text-sm text-gray-500">
                    Customer since {{ new Date(order.customer?.created_at || '').getFullYear() }}
                  </p>
                </div>
                <div class="space-y-2">
                  <div class="flex items-center gap-2 text-sm text-gray-600">
                    <Mail class="h-4 w-4" />
                    <span>{{ order.customer?.email }}</span>
                  </div>
                  <div class="flex items-center gap-2 text-sm text-gray-600">
                    <Phone class="h-4 w-4" />
                    <span>{{ order.customer?.phone }}</span>
                  </div>
                </div>
              </div>
            </BaseCardContent>
          </BaseCard>

          <!-- Shipping Address -->
          <BaseCard>
            <BaseCardHeader class="pb-4">
              <BaseCardTitle class="flex items-center gap-2 text-lg font-medium">
                <MapPin class="h-5 w-5 text-gray-600" />
                Shipping Address
              </BaseCardTitle>
            </BaseCardHeader>
            <BaseCardContent>
              <div class="text-sm text-gray-900">
                <p class="font-medium">
                  {{ order.customer?.first_name }} {{ order.customer?.last_name }}
                </p>
                <p class="mt-1">
                  {{ order.address }}
                </p>
                <p>{{ order.city }}, {{ order.state }} {{ order.zip }}</p>
                <p>{{ order.country }}</p>
              </div>
            </BaseCardContent>
          </BaseCard>

          <!-- Payment Information -->
          <BaseCard>
            <BaseCardHeader class="pb-4">
              <BaseCardTitle class="flex items-center gap-2 text-lg font-medium">
                <CreditCard class="h-5 w-5 text-gray-600" />
                Payment Information
              </BaseCardTitle>
            </BaseCardHeader>
            <BaseCardContent>
              <div class="space-y-3">
                <div>
                  <label class="text-sm font-medium text-gray-600">Payment Method</label>
                  <p class="mt-1 text-sm text-gray-900">
                    {{ order.payment_method || 'N/A' }}
                  </p>
                </div>
                <div>
                  <label class="text-sm font-medium text-gray-600">Currency</label>
                  <p class="mt-1 text-sm text-gray-900">
                    {{ order.currency || 'USD' }}
                  </p>
                </div>
                <div v-if="order.discount_code">
                  <label class="text-sm font-medium text-gray-600">Discount Code</label>
                  <p class="mt-1 text-sm text-gray-900">
                    {{ order.discount_code }}
                  </p>
                </div>
              </div>
            </BaseCardContent>
          </BaseCard>
        </div>
      </div>
    </div>

    <div v-else>
      <div class="flex items-center justify-center h-screen">
        <div class="text-center">
          <h1 class="text-2xl font-bold">
            Order not found
          </h1>
        </div>
      </div>
    </div>
  </div>
</template>
