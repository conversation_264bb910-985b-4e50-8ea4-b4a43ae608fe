<script setup lang="ts">
import { Button } from '~/components/ui/button'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '~/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'
import { Plus, Minus, RotateCcw } from 'lucide-vue-next'

interface Props {
  variant: Variant
}

const props = defineProps<Props>()
const emit = defineEmits<{
  stockUpdated: [variant: Variant]
}>()

const isOpen = ref(false)
const requesting = ref(false)
const operation = ref<'set' | 'increase' | 'decrease'>('set')
const quantity = ref(0)

const operationLabels = {
  set: 'Set Stock To',
  increase: 'Increase Stock By',
  decrease: 'Decrease Stock By'
}

async function updateStock() {
  try {
    requesting.value = true
    const { getDefaultHeaders } = useCustomFetch()
    const { notifySuccess, notifyError } = useToast()

    let endpoint = ''
    let method = 'PUT'
    
    switch (operation.value) {
      case 'set':
        endpoint = `/api/v1/admin/variants/${props.variant.id}/stock`
        method = 'PUT'
        break
      case 'increase':
        endpoint = `/api/v1/admin/variants/${props.variant.id}/stock/increase`
        method = 'POST'
        break
      case 'decrease':
        endpoint = `/api/v1/admin/variants/${props.variant.id}/stock/decrease`
        method = 'POST'
        break
    }

    const updatedVariant = await $fetch(endpoint, {
      method,
      headers: getDefaultHeaders(),
      body: { quantity: quantity.value }
    })

    emit('stockUpdated', updatedVariant)
    notifySuccess(`Stock ${operation.value === 'set' ? 'updated' : operation.value + 'd'} successfully`)
    isOpen.value = false
    quantity.value = 0
  } catch (error: any) {
    console.error(error)
    const message = error?.data?.message || 'Failed to update stock'
    notifyError(message)
  } finally {
    requesting.value = false
  }
}

function resetForm() {
  operation.value = 'set'
  quantity.value = 0
}

watch(isOpen, (newValue) => {
  if (!newValue) {
    resetForm()
  }
})
</script>

<template>
  <Dialog v-model:open="isOpen">
    <DialogTrigger as-child>
      <Button variant="outline" size="sm">
        <RotateCcw class="h-4 w-4 mr-2" />
        Manage Stock
      </Button>
    </DialogTrigger>
    
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle>Manage Stock - {{ variant.sku }}</DialogTitle>
        <DialogDescription>
          Current stock: {{ variant.quantity || 0 }} units
        </DialogDescription>
      </DialogHeader>
      
      <div class="space-y-4">
        <div class="space-y-2">
          <Label for="operation">Operation</Label>
          <Select v-model="operation">
            <SelectTrigger>
              <SelectValue placeholder="Select operation" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="set">
                <div class="flex items-center gap-2">
                  <RotateCcw class="h-4 w-4" />
                  Set Stock To
                </div>
              </SelectItem>
              <SelectItem value="increase">
                <div class="flex items-center gap-2">
                  <Plus class="h-4 w-4" />
                  Increase Stock By
                </div>
              </SelectItem>
              <SelectItem value="decrease">
                <div class="flex items-center gap-2">
                  <Minus class="h-4 w-4" />
                  Decrease Stock By
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div class="space-y-2">
          <Label for="quantity">{{ operationLabels[operation] }}</Label>
          <Input
            id="quantity"
            v-model.number="quantity"
            type="number"
            min="0"
            placeholder="Enter quantity"
            :disabled="requesting"
          />
        </div>

        <Card v-if="operation && quantity > 0" class="bg-muted/50">
          <CardContent class="pt-4">
            <div class="text-sm">
              <div class="flex justify-between">
                <span>Current Stock:</span>
                <span class="font-medium">{{ variant.quantity || 0 }}</span>
              </div>
              <div class="flex justify-between">
                <span>{{ operationLabels[operation] }}:</span>
                <span class="font-medium">{{ quantity }}</span>
              </div>
              <div class="border-t pt-2 mt-2 flex justify-between font-semibold">
                <span>New Stock:</span>
                <span>
                  {{
                    operation === 'set' 
                      ? quantity 
                      : operation === 'increase' 
                        ? (variant.quantity || 0) + quantity
                        : Math.max(0, (variant.quantity || 0) - quantity)
                  }}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <DialogFooter>
        <Button 
          variant="outline" 
          @click="isOpen = false"
          :disabled="requesting"
        >
          Cancel
        </Button>
        <Button 
          @click="updateStock"
          :disabled="requesting || !quantity || quantity <= 0"
        >
          {{ requesting ? 'Updating...' : 'Update Stock' }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
