<script setup lang="ts">
const props = defineProps({
  total: {
    type: Number,
    required: true,
  },
  current: {
    type: Number,
    required: true,
  },
})

const route = useRoute()
const { setQueryParam } = useQueryParams()

// Get perPage from query parameters with default fallback
const perPage = computed(() => Number(route.query.per_page) || 25)

function onChangePage(page: number) {
  if (page !== props.current) {
    setQueryParam({
      page: page.toString(),
    })
  }
}
</script>

<template>
  <div
    class="flex justify-between"
    :class="{
      '!justify-end': total <= 1,
    }"
  >
    <BasePagination
      v-if="total > 1"
      v-slot="{ page }"
      :total="total"
      :sibling-count="1"
      show-edges
      :page="current"
      :default-page="current"
      :items-per-page="perPage"
    >
      <BasePaginationList v-slot="{ items }" class="flex items-center gap-1">
        <BasePaginationPrev />

        <template v-for="(item, index) in items">
          <BasePaginationListItem
            v-if="item.type === 'page'"
            :key="index"
            :value="item.value"
            as-child
            @click="onChangePage(item.value)"
          >
            <BaseButton
              class="w-9 h-9 p-0"
              size="sm"
              :variant="item.value === page ? 'default' : 'outline'"
            >
              {{ item.value }}
            </BaseButton>
          </BasePaginationListItem>
          <BasePagination-ellipsis v-else :key="item.type" :index="index" />
        </template>

        <BasePaginationNext />
      </BasePaginationList>
    </BasePagination>
    <TablePaginationPerPage :per-page="perPage" />
  </div>
</template>
