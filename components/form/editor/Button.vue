<script setup lang="ts">
defineProps<{
  delayDuration?: number
  tooltipContent: string
  isDisabled?: boolean
}>()
</script>

<template>
  <BaseTooltipProvider>
    <BaseTooltip :delay-duration="delayDuration || 0.1">
      <BaseTooltipTrigger
        :class="{
          'cursor-not-allowed': isDisabled,
          'opacity-50': isDisabled,
        }"
        @click="$emit('onContentClick')"
      >
        <div class="hover:bg-gray-300 w-7 h-7 flex items-center justify-center rounded">
          <slot />
        </div>
      </BaseTooltipTrigger>
      <BaseTooltipContent>
        {{ tooltipContent }}
      </BaseTooltipContent>
    </BaseTooltip>
  </BaseTooltipProvider>
</template>
