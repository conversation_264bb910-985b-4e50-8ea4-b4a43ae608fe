<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import { getLocaleContent } from '~/config/locales'

const props = defineProps<{
  modelValue: string[]
}>()
const emit = defineEmits(['update:modelValue'])
const current = useVModel(props, 'modelValue', emit)

const settingsStore = useSettingsStore()
const collections = computed(() => settingsStore.collections)

// Convert collection names to display format
const collectionNames = computed(() =>
  collections.value.map(collection => getLocaleContent(collection.name)),
)
</script>

<template>
  <FormInputTags
    v-model="current"
    label="Collection"
    :predefined-tags="collectionNames"
    placeholder="Enter collection..."
  />
</template>
