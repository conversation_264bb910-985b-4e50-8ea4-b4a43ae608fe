<script lang="ts" setup>
import type { SaleEventListItem } from '~/types/event'
import { Calendar, Package, Percent, Plus } from 'lucide-vue-next'

definePageMeta({
  middleware: ['auth'],
})

const tableSchema = [
  {
    key: 'name',
    label: 'Event Name',
  },
  {
    key: 'duration',
    label: 'Duration',
  },
  {
    key: 'discount',
    label: 'Discount',
  },
  {
    key: 'products',
    label: 'Products',
  },
  {
    key: 'status',
    label: 'Status',
  },
  {
    key: 'updatedAt',
    label: 'Updated',
  },
]

const { getDefaultHeaders, clientFetch } = useCustomFetch()
const { notifySuccess, notifyError } = useToast()
const route = useRoute()
const { setQueryParam } = useQueryParams()

// Pagination params
const routeQuery = computed(() => route.query)

// Fetch sale events with pagination and filters
const { data: pagination, refresh } = await useAsyncData(
  'sale-events',
  async () => {
    try {
      const { data } = await $fetch<ResponseData<Pagination<SaleEventListItem[]>>>('/api/sale-events', {
        headers: getDefaultHeaders(),
        params: {
          page: routeQuery.value.page,
          per_page: routeQuery.value.per_page,
          search: routeQuery.value.q,
          status: routeQuery.value.status === 'all' ? undefined : routeQuery.value.status,
          filter_type: routeQuery.value.filter_type === 'all' ? undefined : routeQuery.value.filter_type,
        },
      })
      return data
    }
    catch (error) {
      console.error('Failed to fetch sale events:', error)
      return null
    }
  },
  {
    watch: [routeQuery],
  },
)

const events = computed(() => pagination.value?.data || [])

// Delete event with optimistic UI update
const deletingIds = ref<number[]>([])
const optimisticEvents = computed(() => {
  if (!events.value)
    return []
  return events.value.filter(event => !deletingIds.value.includes(event.id))
})

async function doDelete(item: SaleEventListItem) {
  // Optimistically remove from UI
  deletingIds.value.push(item.id)

  try {
    const success = await clientFetch<ResponseData<boolean>>(`/api/sale-events/${item.id}`, {
      method: 'DELETE',
    })

    if (success) {
      notifySuccess('Sale event deleted successfully')
      // Refresh data after successful deletion
      refresh()
    }
    else {
      // Roll back on failure
      notifyError('Failed to delete sale event. It may have products that have been ordered.')
      deletingIds.value = deletingIds.value.filter(id => id !== item.id)
    }
  }
  catch (error) {
    // Roll back on error
    console.error('Failed to delete sale event:', error)
    notifyError('Failed to delete sale event')
    deletingIds.value = deletingIds.value.filter(id => id !== item.id)
  }
}

// Toggle status
async function toggleStatus(item: SaleEventListItem) {
  try {
    const success = await clientFetch(`/api/sale-events/${item.id}/toggle-status`, {
      method: 'PUT',
    })

    if (success) {
      notifySuccess(`Sale event ${item.status ? 'deactivated' : 'activated'}`)
      refresh()
    }
    else {
      notifyError('Failed to update sale event status')
    }
  }
  catch (error) {
    console.error('Failed to toggle status:', error)
    notifyError('Failed to update sale event status')
  }
}

// Format date for display
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString()
}

function formatDateRange(startDate: string, endDate: string) {
  const start = new Date(startDate)
  const end = new Date(endDate)
  const now = new Date()

  const formatOptions: Intl.DateTimeFormatOptions = {
    month: 'short',
    day: 'numeric',
    year: start.getFullYear() !== now.getFullYear() ? 'numeric' : undefined,
  }

  return `${start.toLocaleDateString(undefined, formatOptions)} - ${end.toLocaleDateString(undefined, formatOptions)}`
}

// Search and filter states
const searchQuery = ref(routeQuery.value.q as string || '')
const statusFilter = ref((routeQuery.value.status as string) || 'all')
const filterType = ref((routeQuery.value.filter_type as string) || 'all')

const debouncedSearch = debounce(() => {
  setQueryParam({
    q: searchQuery.value,
    page: '1',
  })
}, 1000)

watch(() => searchQuery.value, () => {
  debouncedSearch()
})

watch(() => statusFilter.value, () => {
  setQueryParam({
    status: statusFilter.value === 'all' ? undefined : statusFilter.value,
    page: '1',
  })
})

watch(() => filterType.value, () => {
  setQueryParam({
    filter_type: filterType.value === 'all' ? undefined : filterType.value,
    page: '1',
  })
})

// Get status badge variant
function getStatusVariant(event: SaleEventListItem) {
  if (!event.status)
    return 'secondary'
  if (event.is_active)
    return 'default'
  if (event.is_upcoming)
    return 'secondary'
  if (event.is_expired)
    return 'destructive'
  return 'secondary'
}

function getStatusText(event: SaleEventListItem) {
  if (!event.status)
    return 'Inactive'
  if (event.is_active)
    return 'Active'
  if (event.is_upcoming)
    return 'Upcoming'
  if (event.is_expired)
    return 'Expired'
  return 'Inactive'
}
</script>

<template>
  <div>
    <div class="flex justify-between items-center my-4">
      <h1 class="text-2xl font-bold">
        Sale Events
      </h1>
      <NuxtLink to="/events/create">
        <BaseButton>
          <Plus class="w-4 h-4 mr-2" />
          Create Sale Event
        </BaseButton>
      </NuxtLink>
    </div>

    <!-- Filters Section -->
    <div class="mb-6 space-y-4">
      <div class="flex gap-4 items-end">
        <div class="flex-1 max-w-md">
          <BaseLabel>Search</BaseLabel>
          <SearchWithQuery
            v-model="searchQuery"
            placeholder="Search by event name..."
          />
        </div>

        <div class="w-48">
          <BaseLabel>Status</BaseLabel>
          <BaseSelect v-model="statusFilter">
            <BaseSelectTrigger>
              <BaseSelectValue placeholder="All statuses" />
            </BaseSelectTrigger>
            <BaseSelectContent>
              <BaseSelectItem value="all">
                All statuses
              </BaseSelectItem>
              <BaseSelectItem value="true">
                Active
              </BaseSelectItem>
              <BaseSelectItem value="false">
                Inactive
              </BaseSelectItem>
            </BaseSelectContent>
          </BaseSelect>
        </div>

        <div class="w-48">
          <BaseLabel>Type</BaseLabel>
          <BaseSelect v-model="filterType">
            <BaseSelectTrigger>
              <BaseSelectValue placeholder="All types" />
            </BaseSelectTrigger>
            <BaseSelectContent>
              <BaseSelectItem value="all">
                All types
              </BaseSelectItem>
              <BaseSelectItem value="current">
                Currently Active
              </BaseSelectItem>
              <BaseSelectItem value="upcoming">
                Upcoming
              </BaseSelectItem>
              <BaseSelectItem value="expired">
                Expired
              </BaseSelectItem>
              <BaseSelectItem value="active">
                Active Status
              </BaseSelectItem>
            </BaseSelectContent>
          </BaseSelect>
        </div>
      </div>
    </div>

    <BaseCard>
      <BaseCardContent>
        <TableTemplate :columns="tableSchema" :data="optimisticEvents">
          <template #name="{ item }">
            <NuxtLink :to="`/events/edit/${item.id}`" class="font-medium hover:underline text-primary">
              {{ item.name }}
            </NuxtLink>
            <div v-if="item.description" class="text-sm text-muted-foreground mt-1 line-clamp-1">
              {{ item.description }}
            </div>
          </template>

          <template #duration="{ item }">
            <div class="flex items-center text-sm">
              <Calendar class="w-4 h-4 mr-2 text-muted-foreground" />
              {{ formatDateRange(item.start_date, item.end_date) }}
            </div>
          </template>

          <template #discount="{ item }">
            <div class="flex items-center font-medium">
              <Percent class="w-4 h-4 mr-1 text-muted-foreground" />
              {{ item.sale_percent }}%
            </div>
          </template>

          <template #products="{ item }">
            <div class="flex items-center">
              <Package class="w-4 h-4 mr-2 text-muted-foreground" />
              <span class="font-medium">{{ item.products_count || 0 }}</span>
            </div>
          </template>

          <template #status="{ item }">
            <div class="flex items-center gap-2">
              <BaseBadge :variant="getStatusVariant(item)">
                {{ getStatusText(item) }}
              </BaseBadge>
              <BaseSwitch
                :model-value="item.status"
                @update:model-value="() => toggleStatus(item)"
              />
            </div>
          </template>

          <template #updatedAt="{ item }">
            {{ item.updated_at ? formatDate(item.updated_at) : '-' }}
          </template>

          <template #actions="{ item }">
            <TableButtonEdit :url="`/events/edit/${item.id}`" />
            <TableButtonDelete @delete="() => doDelete(item)" />
          </template>
        </TableTemplate>

        <!-- Pagination -->
        <div v-if="pagination && pagination.total > 0" class="mt-6">
          <TablePagination
            :total="pagination?.total || 0"
            :current="Number(routeQuery.page || 1)"
          />
        </div>
      </BaseCardContent>
    </BaseCard>
  </div>
</template>
