<script setup lang="ts">
import { SUPPORTED_LOCALES, DEFAULT_LOCALE } from '~/config/locales'

defineProps<{
  modelValue: string
}>()
defineEmits(['update:modelValue'])

const gridColsClass = computed(() => {
  const count = SUPPORTED_LOCALES.length
  return `grid-cols-${count}`
})
</script>

<template>
  <BaseTabs
    :model-value="modelValue"
    :default-value="DEFAULT_LOCALE"
    @update:model-value="$emit('update:modelValue', $event)"
  >
    <BaseTabsList class="grid w-full" :class="gridColsClass">
      <BaseTabsTrigger
        v-for="locale in SUPPORTED_LOCALES"
        :key="locale.code"
        class="text-xs py-1"
        :value="locale.code"
      >
        {{ locale.displayName }}
      </BaseTabsTrigger>
    </BaseTabsList>
  </BaseTabs>
</template>
