interface FetchOption {
  method?: HttpMethod
  body?: object
  params?: object
}

export default function useCustomFetch() {
  const { notifyError, notifyUnexpectedError } = useToast()

  async function clientFetch<T>(url: string, options: FetchOption): Promise<T | null> {
    try {
      const response = await $fetch<ResponseData<T>>(url, {
        ...options,
        headers: getDefaultHeaders(),
        // Don't throw on HTTP error statuses - we want to handle them ourselves
        ignoreResponseError: true,
      })

      // Handle case where response might be returned as a string instead of parsed JSON
      let parsedResponse = response
      if (typeof response === 'string') {
        try {
          parsedResponse = JSON.parse(response) as ResponseData<T>
        }
        catch (parseError) {
          console.error('Failed to parse response as JSON:', parseError)
          notifyUnexpectedError()
          return null
        }
      }

      if (!parsedResponse.success) {
        notifyError(parsedResponse.message || 'An error occurred')
        return null
      }

      return parsedResponse.data
    }
    catch (error: any) {
      // <PERSON>le fetch errors that contain specific error messages from the API
      if (error?.data?.message) {
        // Server returned a structured error response
        notifyError(error.data.message)
      }
      else if (error?.data?.error) {
        // Alternative error structure
        notifyError(error.data.error)
      }
      else if (error?.response?._data?.message) {
        // Another possible error structure
        notifyError(error.response._data.message)
      }
      else if (error?.response?._data?.error) {
        // Another alternative error structure
        notifyError(error.response._data.error)
      }
      else if (error?.message && error.message !== 'fetch failed') {
        // Use the error message if it's meaningful (not just "fetch failed")
        notifyError(error.message)
      }
      else {
        // Fall back to generic error message
        notifyUnexpectedError()
      }

      console.error('Fetch error:', error)
      return null
    }
  }

  function getDefaultHeaders() {
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    } as Record<string, string>

    const token = useCookie('token')
    if (token.value) {
      headers.Authorization = `Bearer ${token.value}`
    }

    return headers
  }

  return { clientFetch, getDefaultHeaders }
}
