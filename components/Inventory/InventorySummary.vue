<script setup lang="ts">
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Badge } from '~/components/ui/badge'
import { Package, AlertTriangle, TrendingDown, TrendingUp } from 'lucide-vue-next'

interface InventoryStats {
  totalVariants: number
  inStockVariants: number
  lowStockVariants: number
  outOfStockVariants: number
  totalQuantity: number
}

const props = defineProps<{
  stats: InventoryStats
}>()

const stockPercentage = computed(() => {
  if (props.stats.totalVariants === 0) return 0
  return Math.round((props.stats.inStockVariants / props.stats.totalVariants) * 100)
})

const lowStockPercentage = computed(() => {
  if (props.stats.totalVariants === 0) return 0
  return Math.round((props.stats.lowStockVariants / props.stats.totalVariants) * 100)
})
</script>

<template>
  <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
    <!-- Total Variants -->
    <Card>
      <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle class="text-sm font-medium">Total Variants</CardTitle>
        <Package class="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div class="text-2xl font-bold">{{ stats.totalVariants }}</div>
        <p class="text-xs text-muted-foreground">
          Product variants in system
        </p>
      </CardContent>
    </Card>

    <!-- In Stock -->
    <Card>
      <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle class="text-sm font-medium">In Stock</CardTitle>
        <TrendingUp class="h-4 w-4 text-green-600" />
      </CardHeader>
      <CardContent>
        <div class="text-2xl font-bold text-green-600">{{ stats.inStockVariants }}</div>
        <p class="text-xs text-muted-foreground">
          {{ stockPercentage }}% of total variants
        </p>
      </CardContent>
    </Card>

    <!-- Low Stock -->
    <Card>
      <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle class="text-sm font-medium">Low Stock</CardTitle>
        <AlertTriangle class="h-4 w-4 text-yellow-600" />
      </CardHeader>
      <CardContent>
        <div class="text-2xl font-bold text-yellow-600">{{ stats.lowStockVariants }}</div>
        <p class="text-xs text-muted-foreground">
          {{ lowStockPercentage }}% need attention
        </p>
      </CardContent>
    </Card>

    <!-- Out of Stock -->
    <Card>
      <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle class="text-sm font-medium">Out of Stock</CardTitle>
        <TrendingDown class="h-4 w-4 text-red-600" />
      </CardHeader>
      <CardContent>
        <div class="text-2xl font-bold text-red-600">{{ stats.outOfStockVariants }}</div>
        <p class="text-xs text-muted-foreground">
          Require immediate restocking
        </p>
      </CardContent>
    </Card>
  </div>

  <!-- Additional Summary -->
  <Card class="mt-4">
    <CardHeader>
      <CardTitle>Inventory Overview</CardTitle>
      <CardDescription>Current stock status across all variants</CardDescription>
    </CardHeader>
    <CardContent>
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <span class="text-sm font-medium">Total Stock Quantity</span>
          <Badge variant="outline" class="text-lg px-3 py-1">
            {{ stats.totalQuantity.toLocaleString() }} units
          </Badge>
        </div>
        
        <div class="space-y-2">
          <div class="flex items-center justify-between text-sm">
            <span>Stock Health</span>
            <span class="font-medium">{{ stockPercentage }}% variants in stock</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div 
              class="bg-green-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${stockPercentage}%` }"
            ></div>
          </div>
        </div>

        <div v-if="stats.lowStockVariants > 0" class="flex items-center gap-2 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
          <AlertTriangle class="h-4 w-4 text-yellow-600" />
          <span class="text-sm text-yellow-800">
            {{ stats.lowStockVariants }} variant{{ stats.lowStockVariants > 1 ? 's' : '' }} running low on stock
          </span>
        </div>

        <div v-if="stats.outOfStockVariants > 0" class="flex items-center gap-2 p-3 bg-red-50 rounded-lg border border-red-200">
          <TrendingDown class="h-4 w-4 text-red-600" />
          <span class="text-sm text-red-800">
            {{ stats.outOfStockVariants }} variant{{ stats.outOfStockVariants > 1 ? 's' : '' }} out of stock
          </span>
        </div>
      </div>
    </CardContent>
  </Card>
</template>
