<script setup lang="ts">
import { AlertCircle, CheckCircle, Circle, Languages } from 'lucide-vue-next'
import { computed, ref } from 'vue'
import { createEmptyLocaleContent, DEFAULT_LOCALE, SUPPORTED_LOCALES } from '~/config/locales'

interface Props {
  modelValue: LocaleContent
  label: string
  placeholder?: string
  required?: boolean
  disabled?: boolean
  error?: any
  helpText?: string
  type?: 'input' | 'textarea'
  rows?: number
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '',
  required: false,
  disabled: false,
  type: 'input',
  rows: 3,
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: LocaleContent): void
}>()

const currentLocale = ref<SupportLocale>(DEFAULT_LOCALE)

const localValue = computed({
  get: () => props.modelValue || createEmptyLocaleContent() as LocaleContent,
  set: (value: LocaleContent) => emit('update:modelValue', value),
})

// Computed properties for better UX
const translationStats = computed(() => {
  const filled = SUPPORTED_LOCALES.filter(locale =>
    localValue.value[locale.code]?.trim(),
  ).length
  const total = SUPPORTED_LOCALES.length
  const percentage = Math.round((filled / total) * 100)

  return {
    filled,
    total,
    percentage,
    isComplete: filled === total,
    hasRequired: localValue.value[DEFAULT_LOCALE]?.trim(),
  }
})

const currentLocaleInfo = computed(() =>
  SUPPORTED_LOCALES.find(locale => locale.code === currentLocale.value),
)

function getLocaleStatus(localeCode: string) {
  const hasContent = localValue.value[localeCode]?.trim()
  const isRequired = localeCode === DEFAULT_LOCALE && props.required
  const isActive = localeCode === currentLocale.value

  return {
    hasContent,
    isRequired,
    isActive,
    isEmpty: !hasContent,
    icon: hasContent ? CheckCircle : (isRequired ? AlertCircle : Circle),
    statusClass: hasContent
      ? 'text-green-600'
      : (isRequired ? 'text-amber-500' : 'text-gray-400'),
  }
}

function updateLocaleValue(value: string) {
  const newValue = { ...localValue.value }
  newValue[currentLocale.value] = value
  localValue.value = newValue as LocaleContent
}

function getInputClasses() {
  if (props.required && currentLocale.value === DEFAULT_LOCALE && !localValue.value[currentLocale.value]?.trim()) {
    return 'border-amber-300 focus:border-amber-500 focus:ring-amber-500'
  }
  if (localValue.value[currentLocale.value]?.trim()) {
    return 'border-green-300 focus:border-green-500 focus:ring-green-500'
  }
  return 'border-gray-300'
}
</script>

<template>
  <div class="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
    <!-- Header with title and progress -->
    <div class="px-4 py-3 bg-gray-50 border-b border-gray-200">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <Languages class="h-4 w-4 text-gray-600" />
          <span class="text-sm font-medium text-gray-900">{{ label }}</span>
          <span v-if="props.required" class="text-red-500">*</span>
        </div>
        <div class="flex items-center gap-3">
          <!-- Translation Progress -->
          <div class="flex items-center gap-2 text-xs text-gray-600">
            <div class="flex items-center gap-1">
              <div class="w-2 h-2 rounded-full bg-gray-200">
                <div
                  class="h-full rounded-full bg-green-500 transition-all duration-300"
                  :style="{ width: `${translationStats.percentage}%` }"
                />
              </div>
              <span>{{ translationStats.filled }}/{{ translationStats.total }}</span>
            </div>
            <span class="text-gray-400">•</span>
            <span :class="translationStats.hasRequired || !props.required ? 'text-green-600' : 'text-amber-600'">
              {{ translationStats.hasRequired || !props.required ? 'Required ✓' : 'Required missing' }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Locale Tabs with Status -->
    <div class="border-b border-gray-200">
      <nav class="flex -mb-px">
        <button
          v-for="locale in SUPPORTED_LOCALES"
          :key="locale.code"
          class="relative flex items-center gap-2 px-4 py-3 text-sm font-medium transition-all duration-200 hover:bg-gray-50"
          :class="[
            currentLocale === locale.code
              ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
              : 'text-gray-600 hover:text-gray-800',
            getLocaleStatus(locale.code).hasContent
              ? 'bg-green-50/50'
              : '',
          ]"
          @click="currentLocale = locale.code"
        >
          <component
            :is="getLocaleStatus(locale.code).icon"
            class="h-3 w-3"
            :class="getLocaleStatus(locale.code).statusClass"
          />
          <span>{{ locale.displayName }}</span>
          <span
            v-if="locale.code === DEFAULT_LOCALE && props.required"
            class="text-xs bg-blue-100 text-blue-700 px-1.5 py-0.5 rounded"
          >
            Required
          </span>
        </button>
      </nav>
    </div>

    <!-- Input Area -->
    <div class="p-4">
      <div class="space-y-2">
        <div class="flex items-center justify-between">
          <label
            :for="`translatable-${currentLocale}`"
            class="text-sm font-medium text-gray-700"
          >
            {{ currentLocaleInfo?.displayName }} {{ label }}
          </label>
          <div
            v-if="!getLocaleStatus(currentLocale).hasContent && currentLocale !== DEFAULT_LOCALE"
            class="text-xs text-gray-500 italic"
          >
            Optional - will fallback to {{ SUPPORTED_LOCALES.find(l => l.code === DEFAULT_LOCALE)?.displayName }} if empty
          </div>
        </div>

        <!-- Input Field -->
        <FormInput
          v-if="type === 'input'"
          :id="`translatable-${currentLocale}`"
          :model-value="localValue[currentLocale] || ''"
          :placeholder="placeholder || `Enter ${label.toLowerCase()} in ${currentLocaleInfo?.name}...`"
          class="w-full transition-all duration-200"
          :class="getInputClasses()"
          :error="error && currentLocale === DEFAULT_LOCALE"
          :error-message="false"
          :disabled="disabled"
          @update:model-value="updateLocaleValue"
        />

        <!-- Textarea Field -->
        <FormTextarea
          v-else-if="type === 'textarea'"
          :id="`translatable-${currentLocale}`"
          :model-value="localValue[currentLocale] || ''"
          :placeholder="placeholder || `Enter ${label.toLowerCase()} in ${currentLocaleInfo?.name}...`"
          class="w-full transition-all duration-200"
          :class="getInputClasses()"
          :rows="rows"
          :error="error && currentLocale === DEFAULT_LOCALE"
          :error-message="false"
          :disabled="disabled"
          @update:model-value="updateLocaleValue"
        />

        <!-- Contextual Help Text -->
        <div class="text-xs text-gray-500">
          <span v-if="helpText">
            {{ helpText }}
          </span>
          <span v-else-if="currentLocale === DEFAULT_LOCALE && props.required">
            This is the primary {{ label.toLowerCase() }} and is required.
          </span>
          <span v-else-if="localValue[currentLocale]?.trim()">
            This {{ label.toLowerCase() }} will be used when displaying content in {{ currentLocaleInfo?.name }}.
          </span>
          <span v-else>
            Add a {{ currentLocaleInfo?.name }} translation for this {{ label.toLowerCase() }}.
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
