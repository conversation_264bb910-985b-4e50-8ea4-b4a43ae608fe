<script setup lang="ts">
import type { CalendarDate as CalendarDateType } from '@internationalized/date'
import { cn } from '@/lib/utils'
import { CalendarDate, DateFormatter, getLocalTimeZone } from '@internationalized/date'
import { Calendar as CalendarIcon } from 'lucide-vue-next'
import { computed } from 'vue'

const props = defineProps<{
  modelValue: string | undefined
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: string | undefined): void
}>()

const df = new DateFormatter('en-US', {
  dateStyle: 'long',
})

const internalDate = computed({
  get() {
    if (!props.modelValue)
      return undefined
    const [year, month, day] = props.modelValue.split('-').map(Number)
    const date = new CalendarDate(year, month, day)
    return date
  },
  set(newDate: CalendarDateType | undefined) {
    if (newDate) {
      const year = newDate.year
      const month = newDate.month
      const day = newDate.day
      const dateString = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
      emit('update:modelValue', dateString)
    }
    else {
      emit('update:modelValue', undefined)
    }
  },
})
</script>

<template>
  <BasePopover>
    <BasePopoverTrigger as-child>
      <BaseButton
        variant="outline"
        :class="cn(
          'w-[280px] justify-start text-left font-normal',
          !internalDate && 'text-muted-foreground',
        )"
      >
        <CalendarIcon class="mr-2 h-4 w-4" />
        {{ internalDate ? df.format(internalDate.toDate(getLocalTimeZone())) : "Pick a date" }}
      </BaseButton>
    </BasePopoverTrigger>
    <BasePopoverContent class="w-auto p-0">
      <BaseCalendar v-model="internalDate" initial-focus />
    </BasePopoverContent>
  </BasePopover>
</template>
