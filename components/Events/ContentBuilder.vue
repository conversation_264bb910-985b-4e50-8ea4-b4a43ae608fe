<script setup lang="ts">
import type { EventRowFormData, ImageAlign, ImageWidth } from '~/types/event'
import { ChevronDown, ChevronUp, GripVertical, Plus, Trash2 } from 'lucide-vue-next'
import useEvents from '~/composables/custom/useEvents'

const props = defineProps<{
  modelValue: EventRowFormData[]
}>()

const emit = defineEmits<{
  'update:modelValue': [value: EventRowFormData[]]
}>()

const MAX_IMAGES_PER_ROW = 2

const {
  widthOptions,
  alignOptions,
} = useEvents()

const { uploadTemp } = useUpload()
const { notifyError } = useToast()

// Local reactive reference to the model value
const rows = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value),
})

// Get visible rows (excluding destroyed ones)
const visibleRows = computed(() => {
  return rows.value.filter(row => !row._destroy)
})

// Get visible images for a row
function getVisibleImages(row: EventRowFormData) {
  return row.event_images.filter(img => !img._destroy)
}

// Add new row
function handleAddRow() {
  const newRow = {
    position: rows.value.length,
    event_images: [],
  }
  rows.value.push(newRow)
}

// Remove row
function handleRemoveRow(index: number) {
  const row = rows.value[index]
  if (row.id) {
    // Mark existing row for deletion
    row._destroy = true
  }
  else {
    // Remove new row directly
    rows.value.splice(index, 1)
    // Update positions
    rows.value.forEach((r, i) => {
      r.position = i
    })
  }
}

// Add image to row
function handleAddImage(rowIndex: number) {
  if (rows.value[rowIndex].event_images.length >= MAX_IMAGES_PER_ROW) {
    notifyError('You can only add up to 2 images per row')
    return
  }

  const row = rows.value[rowIndex]
  if (row && !row._destroy) {
    const newImage = {
      width: 'auto' as ImageWidth,
      align: 'center' as ImageAlign,
      link_url: null,
      position: row.event_images.length,
      file: null,
    }
    row.event_images.push(newImage)
  }
}

// Remove image from row
function handleRemoveImage(rowIndex: number, imageIndex: number) {
  const row = rows.value[rowIndex]
  const image = row.event_images[imageIndex]

  if (image.id) {
    // Mark existing image for deletion
    image._destroy = true
  }
  else {
    // Remove new image directly
    row.event_images.splice(imageIndex, 1)
    // Update positions
    row.event_images.forEach((img, i) => {
      img.position = i
    })
  }
}

// Move row up or down
function handleMoveRow(index: number, direction: 'up' | 'down') {
  const newIndex = direction === 'up' ? index - 1 : index + 1
  if (newIndex >= 0 && newIndex < rows.value.length) {
    const rowsCopy = [...rows.value]
    ;[rowsCopy[index], rowsCopy[newIndex]] = [rowsCopy[newIndex], rowsCopy[index]]

    // Update positions
    rowsCopy.forEach((row, i) => {
      row.position = i
    })

    rows.value = rowsCopy
  }
}

// Handle image upload
function handleImageUpload(rowIndex: number, imageIndex: number) {
  const fileInput = document.querySelector(`input[data-ref="fileInput-${rowIndex}-${imageIndex}"]`) as HTMLInputElement
  fileInput?.click()
}

// Handle file selection
async function onFileSelected(event: Event, rowIndex: number, imageIndex: number) {
  const target = event.target as HTMLInputElement
  const files = target.files
  if (!files || files.length === 0)
    return

  const file = files[0]
  const type = getFileType(file)

  if (!type) {
    notifyError('File type is not supported')
    return
  }

  const urls = await uploadTemp(files)
  if (urls && urls.length > 0) {
    const row = rows.value[rowIndex]
    const image = row.event_images[imageIndex]
    image.file = {
      url: urls[0],
      type,
      id: null,
    }
  }

  // Reset the file input
  target.value = ''
}
</script>

<template>
  <div class="space-y-6">
    <!-- Rows -->
    <div v-for="(row, rowIndex) in visibleRows" :key="`row-${rowIndex}`" class="border rounded-lg p-4 bg-gray-50">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center gap-2">
          <GripVertical class="h-4 w-4 text-gray-400" />
          <h4 class="font-medium">
            Row {{ rowIndex + 1 }}
          </h4>
        </div>
        <div class="flex items-center gap-2">
          <BaseButton
            variant="ghost"
            size="sm"
            :disabled="rowIndex === 0"
            @click="handleMoveRow(rowIndex, 'up')"
          >
            <ChevronUp class="h-4 w-4" />
          </BaseButton>
          <BaseButton
            variant="ghost"
            size="sm"
            :disabled="rowIndex === visibleRows.length - 1"
            @click="handleMoveRow(rowIndex, 'down')"
          >
            <ChevronDown class="h-4 w-4" />
          </BaseButton>
          <BaseButton
            v-if="getVisibleImages(row).length < MAX_IMAGES_PER_ROW"
            variant="ghost"
            size="sm"
            @click="handleAddImage(rowIndex)"
          >
            <Plus class="h-4 w-4" />
            Add Image
          </BaseButton>
          <BaseButton
            variant="ghost"
            size="sm"
            @click="handleRemoveRow(rowIndex)"
          >
            <Trash2 class="h-4 w-4" />
          </BaseButton>
        </div>
      </div>

      <!-- Images in this row -->
      <div class="space-y-4">
        <div
          v-for="(image, imageIndex) in getVisibleImages(row)"
          :key="`image-${rowIndex}-${imageIndex}`"
          class="border rounded-md p-3 bg-white"
        >
          <div class="flex items-center justify-between mb-3">
            <span class="text-sm font-medium">Image {{ imageIndex + 1 }}</span>
            <div class="flex items-center gap-2">
              <BaseButton
                variant="ghost"
                size="xs"
                @click="handleRemoveImage(rowIndex, imageIndex)"
              >
                <Trash2 class="h-3 w-3" />
              </BaseButton>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Width Selection -->
            <div>
              <label class="block text-sm font-medium mb-1">Width</label>
              <BaseSelect v-model="image.width">
                <BaseSelectTrigger>
                  <BaseSelectValue placeholder="Select width" />
                </BaseSelectTrigger>
                <BaseSelectContent>
                  <BaseSelectItem
                    v-for="option in widthOptions"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </BaseSelectItem>
                </BaseSelectContent>
              </BaseSelect>
            </div>

            <!-- Alignment Selection -->
            <div>
              <label class="block text-sm font-medium mb-1">Alignment</label>
              <BaseSelect v-model="image.align">
                <BaseSelectTrigger>
                  <BaseSelectValue placeholder="Select alignment" />
                </BaseSelectTrigger>
                <BaseSelectContent>
                  <BaseSelectItem
                    v-for="option in alignOptions"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </BaseSelectItem>
                </BaseSelectContent>
              </BaseSelect>
            </div>

            <!-- Link URL -->
            <div>
              <label class="block text-sm font-medium mb-1">Link URL (Optional)</label>
              <BaseInput
                :model-value="image.link_url || ''"
                placeholder="https://example.com"
                type="url"
                class="w-full"
                @update:model-value="(value) => image.link_url = (value as string) || null"
              />
            </div>
          </div>

          <!-- Image Upload -->
          <div class="mt-4">
            <label class="block text-sm font-medium mb-2">Image</label>
            <div v-if="!image.file" class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center text-gray-500 hover:border-gray-400 transition-colors cursor-pointer" @click="() => handleImageUpload(rowIndex, imageIndex)">
              <p class="text-sm">
                Click to upload image
              </p>
              <p class="text-xs">
                Supports JPG, PNG, WebP formats
              </p>
            </div>
            <div v-else class="border-2 border-dashed border-gray-300 rounded-lg p-2 relative">
              <FilePreview :file="image.file" />
              <BaseButton
                variant="danger"
                size="xs"
                class="absolute -top-2 -right-2"
                @click="image.file = null"
              >
                <Trash2 class="h-3 w-3" />
              </BaseButton>
            </div>
            <!-- Hidden file input -->
            <input
              :data-ref="`fileInput-${rowIndex}-${imageIndex}`"
              type="file"
              accept="image/*"
              class="hidden"
              @change="(e) => onFileSelected(e, rowIndex, imageIndex)"
            >
          </div>
        </div>

        <!-- Add Image Button -->
        <BaseButton
          v-if="getVisibleImages(row).length === 0"
          variant="outline"
          class="w-full"
          @click="handleAddImage(rowIndex)"
        >
          <Plus class="h-4 w-4 mr-2" />
          Add Image to Row
        </BaseButton>
      </div>
    </div>

    <!-- Add Row Button -->
    <BaseButton
      variant="outline"
      class="w-full"
      @click="handleAddRow"
    >
      <Plus class="h-4 w-4 mr-2" />
      Add New Row
    </BaseButton>
  </div>
</template>
