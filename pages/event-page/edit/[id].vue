<script lang="ts" setup>
import type { Event } from '~/types/event'
import { Save } from 'lucide-vue-next'
import useEvents from '~/composables/custom/useEvents'

definePageMeta({
  middleware: ['auth'],
})

const router = useRouter()
const route = useRoute()
const { clientFetch, getDefaultHeaders } = useCustomFetch()
const { notifyError, notifySuccess } = useToast()

const id = route.params.id as string

const {
  form,
  validate,
  errors,
  setForm,
  generateSlugFromTitle,
} = useEvents()

// Fetch existing event data
const { data: event, error } = await useAsyncData(`event-${id}`, async () => {
  const { data } = await $fetch<ResponseData<Event>>(`/api/event-pages/${id}`, {
    headers: getDefaultHeaders(),
  })
  return data
})

// Redirect if event doesn't exist
if (!event.value && !error.value) {
  router.push('/events')
}

// Set form data when event is loaded
if (event.value) {
  setForm({
    title: event.value.title,
    slug: event.value.slug,
    is_active: event.value.is_active,
    event_rows: event.value.event_rows.map(row => ({
      id: row.id,
      position: row.position,
      event_images: row.event_images.map(image => ({
        id: image.id,
        width: image.width,
        align: image.align,
        link_url: image.link_url,
        position: image.position,
        file: image.image
          ? {
              id: image.image.id,
              url: image.image.url || image.image.path,
              type: image.image.type,
            }
          : null,
      })),
    })),
  })
}

const isSaving = ref(false)

async function submit() {
  if (!validate()) {
    notifyError('Please fill in all required fields.')
    return
  }

  isSaving.value = true
  try {
    const success = await clientFetch(`/api/event-pages/${id}`, {
      method: 'PUT',
      body: toRaw(form),
    })

    if (success) {
      notifySuccess('Event updated successfully')
      router.push('/events')
    }
  }
  catch (error) {
    notifyError('Failed to update event')
    console.error('Error updating event:', error)
  }
  finally {
    isSaving.value = false
  }
}
</script>

<template>
  <div class="pt-1">
    <div class="flex items-center justify-between mb-6">
      <GoBack />
      <BaseButton
        :disabled="isSaving"
        size="sm"
        @click="submit"
      >
        <Save class="h-4 w-4" />
        {{ isSaving ? 'Saving...' : 'Save Event' }}
      </BaseButton>
    </div>

    <BaseCard>
      <BaseCardHeader>
        <BaseCardTitle>
          Edit Event: {{ event?.title }}
        </BaseCardTitle>
      </BaseCardHeader>
      <BaseCardContent>
        <div class="space-y-6">
          <!-- Basic Event Information -->
          <div class="grid gap-4">
            <FormInput
              id="event-title"
              v-model="form.title"
              label="Event Title"
              placeholder="Enter event title"
              required
              :error="errors.title"
              @input="generateSlugFromTitle"
            />

            <FormInput
              id="event-slug"
              v-model="form.slug"
              label="Event Slug"
              placeholder="event-slug"
              required
              :error="errors.slug"
              help-text="URL-friendly version of the title. Only lowercase letters, numbers, and hyphens."
            />

            <div class="flex items-center space-x-2">
              <BaseSwitch
                id="event-active"
                v-model="form.is_active"
              />
              <label for="event-active" class="text-sm font-medium">
                Active
              </label>
            </div>
          </div>

          <!-- Event Content Builder -->
          <div class="mt-8">
            <h3 class="text-lg font-medium mb-4">
              Event Content
            </h3>
            <EventsContentBuilder v-model="form.event_rows" />
          </div>
        </div>
      </BaseCardContent>
    </BaseCard>
  </div>
</template>
