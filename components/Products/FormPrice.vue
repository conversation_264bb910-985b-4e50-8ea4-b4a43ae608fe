<script setup lang="ts">
const settings = useSettingsStore()
const locations = computed(() => settings.locations)

const table = [
  {
    key: 'country',
    label: 'Country',
  },
  {
    key: 'price',
    label: 'Price 1',
  },
  // {
  //   key: 'compare_price',
  //   label: 'Price 2',
  // },
]

const locationPrices = defineModel('locationPrices', {
  type: Array as PropType<BasePriceForm[]>,
  required: true,
})

const price = defineModel('price', {
  type: Number,
  default: 0,
})

const comparePrice = defineModel('compare_price', {
  type: Number,
  default: 0,
})

function getCurrency(id: number) {
  const location = locations.value.find(location => location.id === id)
  return location ? location.currency : 'USD'
}
</script>

<template>
  <BaseCard>
    <BaseCardHeader>
      Pricing
    </BaseCardHeader>

    <BaseCardContent>
      <div class="grid grid-cols-2 gap-6 mb-4 ml-2">
        <div class="space-y-1">
          <div class="text-sm">
            Default Price 1
          </div>
          <FormInputMoney
            id="default-price"
            v-model="price"
            currency="USD"
          />
        </div>
        <!-- <div class="space-y-1">
          <div class="text-sm">
            Default Price 2
          </div>
          <FormInputMoney
            id="compare-price"
            v-model="comparePrice"
          />
        </div> -->
      </div>

      <TableTemplate
        :columns="table"
        :data="locationPrices"
      >
        <template #country="{ item }">
          {{ item.country }}
        </template>
        <template #price="{ item }">
          <FormInputMoney
            :id="`price-${item.country}`"
            v-model="item.price"
            :currency="getCurrency(item.id)"
          />
        </template>
        <!-- <template #compare_price="{ item }">
          <FormInputMoney
            :id="`compare-price-${item.country}`"
            v-model="item.compare_price"
            :currency="getCurrency(item.id)"
          />
        </template> -->
      </TableTemplate>
    </BaseCardContent>
  </BaseCard>
</template>
