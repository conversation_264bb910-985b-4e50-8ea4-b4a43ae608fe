import type { LOCALE_CODES } from '~/config/locales'

declare global {
  type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'OPTIONS' | 'HEAD'
  interface ResponseData<T> {
    success: boolean
    message: string
    data: T | null
  }

  interface QueryParams {
    [key: string]: string | undefined | string[]
  }

  type SupportLocale = typeof LOCALE_CODES[number]
  type LocaleContent = {
    [K in SupportLocale]: string
  }

  interface userPreferences {
    openMenu: boolean
  }

  interface PaginationLink {
    url: string | null
    label: string
    active: boolean
  }

  interface Pagination<T> {
    current_page: number
    data: T
    first_page_url: string
    from: number
    last_page: number
    last_page_url: string
    links: PaginationLink[]
    next_page_url: string | null
    path: string
    per_page: number
    prev_page_url: string | null
    to: number
    total: number
  }

  type FileType = 'image' | 'video'

  interface FileForm {
    id: number | null | undefined
    url: string
    type: FileType
  }

  interface FileDB {
    id: number
    storage: string
    path: string
    type: FileType
    position: number
  }
}

export {}
