export default defineNuxtRouteMiddleware(async () => {
  const userCookie = useCookie('token')

  // check has token
  if (!userCookie.value) {
    return navigateTo('/login')
  }

  // check token is valid
  const { authGuard } = useAuth()
  if (!await authGuard()) {
    return navigateTo('/login')
  }

  // Init backend settings
  const settingsStore = useSettingsStore()
  await settingsStore.fetchSettings()
})
