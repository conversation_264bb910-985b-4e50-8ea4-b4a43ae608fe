# Locale Configuration

This project uses a centralized locale configuration system that makes it easy to add new languages.

## Current Supported Locales

- **English** (`en`) - Default locale
- **Russian** (`ru`)
- **Vietnamese** (`vi`)
- **Chinese** (`zn`)

## Adding a New Locale

To add a new locale (e.g., French), follow these steps:

### 1. Update the Locale Configuration

Edit `config/locales.ts` and add your new locale to the `SUPPORTED_LOCALES` array:

```typescript
export const SUPPORTED_LOCALES: LocaleConfig[] = [
  {
    code: 'en',
    name: 'English',
    displayName: 'English'
  },
  {
    code: 'ru',
    name: 'Russian',
    displayName: 'Russia'
  },
  {
    code: 'vi',
    name: 'Vietnamese',
    displayName: 'Vietnamese'
  },
  {
    code: 'zn',
    name: 'Chinese',
    displayName: 'Chinese'
  },
  // Add your new locale here
  {
    code: 'fr',
    name: 'French',
    displayName: 'Français'
  }
]
```

### 2. Update Tailwind Configuration (if needed)

If you're adding more than 8 locales total, update `tailwind.config.js` to include additional grid classes:

```javascript
safelist: [
  'dark',
  // Add more grid classes if needed
  'grid-cols-9',
  'grid-cols-10',
  // ... etc
],
```

### 3. That's it!

The system will automatically:

- ✅ Update all locale selectors to include the new language
- ✅ Update type definitions (`SupportLocale` and `LocaleContent`)
- ✅ Include the new locale in all forms and components
- ✅ Handle translation status indicators
- ✅ Apply proper fallback logic

## Key Features

- **Centralized Configuration**: All locale settings in one place
- **Type Safety**: TypeScript types are automatically generated
- **Automatic UI Updates**: Locale selectors update automatically
- **Fallback Logic**: Smart fallback to available translations
- **Helper Functions**: Utilities for common locale operations

## Helper Functions

- `getLocaleName(code)` - Get the human-readable name
- `getLocaleDisplayName(code)` - Get the display name for UI
- `createEmptyLocaleContent()` - Create empty locale content object
- `getLocaleContent(content, locale?)` - Get content with fallback logic
- `isValidLocale(code)` - Check if a locale code is valid

## Usage Examples

```typescript
// Create empty locale content
const content = createEmptyLocaleContent()

// Get display text with fallback
const displayText = getLocaleContent(blog.title)

// Check if locale is supported
if (isValidLocale('fr')) {
  // Handle French content
}
```

## Backend Considerations

When adding new locales, ensure your Laravel backend:

1. Updates the `$translatable` array in models
2. Handles the new locale codes in validation
3. Updates any locale-specific business logic
