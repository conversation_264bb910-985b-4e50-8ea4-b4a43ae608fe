declare global {
  type Optional<T> = T | null | undefined
  interface Order {
    id: number
    access_token: Optional<string>
    discount_code: Optional<string>
    customer_id: Optional<number>
    total_price: Optional<number>
    discount_price: Optional<number>
    currency: Optional<string>
    phone: Optional<string>
    address: Optional<string>
    city: Optional<string>
    state: Optional<string>
    zip: Optional<string>
    country: Optional<string>
    status: Optional<string>
    payment_method: Optional<string>
    created_at: Optional<string>
    updated_at: Optional<string>
    customer: Optional<Customer>
    order_products_count: Optional<number>
    products: Optional<ProductListItem[]>
  }

  interface Customer {
    address: string
    city: string
    country: string
    created_at: string
    email: string
    email_verified_at: string | null
    first_name: string
    id: number
    last_name: string
    phone: string
    state: string
    updated_at: string
    zip: string
    draft_orders_count: number
    non_draft_orders_count: number
  }
  interface ProductListItem {
    id: number
    category_id: number
    sku: string
    slug: string
    name: string
    options: ProductOptions
    status: number
    description: string | null
    created_at: string
    updated_at: string
    prices_by_location: PriceByLocation[]
    image: FileDB
    category: Category
    collections: CollectionDB[]
    price: number
    compare_price: number
    priority: number
  }

  interface ProductItem {
    id: number
    category_id: number
    sku: string
    slug: string
    name: string
    options: ProductOptions
    status: number
    description: string | null
    created_at: string
    updated_at: string
    prices_by_location: PriceByLocation[]
    images: FileDB[]
    collections: ProductCollection[]
    category: ProductCategory
    price: number
    compare_price: number
    size_guide_id: number
    variants?: Variant[]
  }

  interface Variant {
    status: 'active' | 'inactive'
    size: string
    adjust_price: number
    sku: string
    sapo_sku: string
    id: number | string
  }

  interface ProductListForCollection {
    id: number
    name: string
    slug: string
    image: FileDB
    sku: string
  }

  interface ProductCategory {
    id: number
    name: string
    slug: string
  }

  interface ProductCollection {
    id: number
    name: string
    slug: string
    menu_id: number
  }

  interface ProductOptions {
    colors: string[]
    sizes: string[]
  }

  interface Category {
    id: number
    name: string
    slug: string
  }

  interface PriceByLocation {
    location_id: number
    location: string
    price: number
    compare_price: number
  }

  interface CollectionDB {
    id: number
    name: string
    slug: string
    status: number
    menu_id: number
  }

  interface BasePriceForm {
    id: number
    price: number
    compare_price: number
    country: string
  }
}

export {}
