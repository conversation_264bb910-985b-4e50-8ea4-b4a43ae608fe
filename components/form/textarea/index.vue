<script setup lang="ts">
defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  title: {
    type: String,
    default: null,
  },
  placeholder: {
    type: String,
    default: '',
  },
  isDisabled: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue'])

function onInput(event: Event) {
  const target = event.target as HTMLTextAreaElement
  emit('update:modelValue', target.value)
}
</script>

<template>
  <div class="relative w-full min-w-[200px]">
    <div v-if="title">
      {{ title }}
    </div>
    <textarea
      class="peer h-full min-h-[100px] w-full resize-none rounded-[7px] border border-blue-gray-200 px-3 py-2.5 font-sans text-sm font-normal text-blue-gray-700 outline outline-0 transition-all placeholder-shown:border placeholder-shown:border-blue-gray-200 placeholder-shown:border-t-blue-gray-200 focus:border-2 focus:outline-0 disabled:resize-none disabled:border-0 disabled:bg-blue-gray-50"
      :placeholder="placeholder"
      :value="modelValue"
      :disabled="isDisabled"
      @input="onInput"
    />
  </div>
</template>
