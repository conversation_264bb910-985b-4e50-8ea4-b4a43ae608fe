<script lang="ts" setup>
import type { BannerDetail, BannerFormUpdate } from '@/types/banner'

definePageMeta({
  middleware: ['auth'],
})

const router = useRouter()

const { clientFetch, getDefaultHeaders } = useCustomFetch()
const { notifyError, notifySuccess } = useToast()

const { form, setForm } = useForm({
  banners: {
    default: [] as BannerFormUpdate[],
    rules: [],
  },
})

const { id } = useRoute().params
const { data: banners } = await useAsyncData(`banner-${id}`, async () => {
  const { data } = await $fetch<ResponseData<BannerDetail[]>>(`/api/banners/${id}`, {
    headers: getDefaultHeaders(),
  })

  return data
})

if (banners.value) {
  setForm({
    banners: banners.value.map((banner) => {
      const file = banner.medias.find(media => media.view === 'desktop')
      const file_mobile = banner.medias.find(media => media.view === 'mobile')

      return {
        id: banner.id,
        url: banner.url,
        file: file ? {
          id: file.id,
          url: file.url,
          type: file.type,
        } : null,
        file_mobile: file_mobile ? {
          id: file_mobile.id,
          url: file_mobile.url,
          type: file_mobile.type,
        } : null,
        group: Number(id),
      }
    }),
  })
}
else {
  notifyError('Failed to fetch banners')
}

async function submit() {
  const result = await clientFetch(`/api/banners/${id}`, {
    method: 'PUT',
    body: toRaw(form),
  })

  if (result) {
    notifySuccess('Banner updated successfully')
    router.push('/banners')
  }
  else {
    notifyError('Failed to update banner')
  }
}
</script>

<template>
  <div>
    <div class="flex items-center justify-between mt-2">
      <div class="text-lg uppercase">
        Edit Banner
      </div>
      <BaseButton @click="submit">
        Save Changes
      </BaseButton>
    </div>
    <BaseCard class="mt-4">
      <BaseCardHeader>
        Banners
      </BaseCardHeader>
      <BaseCardContent>
        <BannerForm v-model:form="form" />
      </BaseCardContent>
    </BaseCard>
  </div>
</template>
