<script setup lang="ts">
import { history } from '@tiptap/pm/history'
import { useVModel } from '@vueuse/core'

const props = defineProps<{
  modelValue: LocaleContent
}>()
const emit = defineEmits(['update:initialContent'])
const { value: langs } = useVModel(props, 'modelValue', emit)

const currentLang = ref<SupportLocale>('en')
const isFocused = ref(false)
const editor = useEditor({
  content: langs[currentLang.value],
  extensions: [TiptapStarterKit],
  onUpdate: ({ editor }) => {
    const rawHtml = editor.getHTML()
    langs[currentLang.value] = rawHtml.replace(/\s+<\/p>/g, (match) => { // replace space to &nbsp; to prevent lost space character
      const count = match.length - 4
      return `${'&nbsp;'.repeat(count)}</p>`
    })
  },
  onFocus() {
    isFocused.value = true
  },
  onBlur() {
    isFocused.value = false
  },
})

watch(currentLang, (newLang) => {
  editor.value?.commands.setContent(langs[newLang])
  clearHistory()
})

function clearHistory() {
  editor.value?.unregisterPlugin('history')
  editor.value?.registerPlugin(history())
  editor.value?.commands.focus('end')
}

onBeforeUnmount(() => {
  unref(editor)?.destroy()
})
</script>

<template>
  <div class="max-w-[600px] space-y-1">
    <FormEditorMenu v-if="editor" :editor="editor" />
    <FormLocaleSelector v-model="currentLang" />
    <TiptapEditorContent
      v-if="editor"
      class="min-h-32 border rounded border-input reverse-preflight p-2"
      :class="{ '!border-ring': isFocused }"
      :editor="editor"
      @click="editor.commands.focus()"
    />
  </div>
</template>
