<script lang="ts" setup>
import { debounce } from '@/utils'
import { createEmptyLocaleContent, DEFAULT_LOCALE, getLocaleContent } from '~/config/locales'

definePageMeta({
  middleware: ['auth'],
})

const route = useRoute()
const collectionId = route.params.id as string
const { clientFetch, getDefaultHeaders } = useCustomFetch()
const { notifySuccess, notifyError } = useToast()

const { data: collection } = await useAsyncData(`collection-${collectionId}`, async () => {
  const { data } = await $fetch<ResponseData<Collection>>(`/api/collections/${collectionId}`, {
    headers: getDefaultHeaders(),
  })
  return data
})

const isSaving = ref(false)

// Convert collection name to LocaleContent format for consistency
function normalizeCollectionName(name: string | LocaleContent | undefined): LocaleContent {
  if (!name) {
    return createEmptyLocaleContent() as LocaleContent
  }

  if (typeof name === 'string') {
    // Convert old string format to LocaleContent format
    const content = createEmptyLocaleContent()
    content[DEFAULT_LOCALE] = name
    return content as LocaleContent
  }

  return name
}

// Initialize the form with the collection data
const form = reactive({
  name: normalizeCollectionName(collection.value?.name),
})

// Debounced update function that handles LocaleContent
const debouncedUpdateCollectionName = debounce(async (nameContent: LocaleContent) => {
  // Check if at least the default locale has content
  const defaultName = nameContent[DEFAULT_LOCALE]?.trim()

  if (!defaultName || defaultName.length < 3) {
    return
  }

  isSaving.value = true

  try {
    const success = await clientFetch(`/api/collections/${collectionId}`, {
      method: 'PUT',
      body: {
        name: nameContent,
      },
    })

    if (success) {
      notifySuccess('Collection name updated')
    }
    else {
      notifyError('Failed to update collection name')
    }
  }
  catch (error) {
    console.error('Error updating collection:', error)
    notifyError('Failed to update collection name')
  }
  finally {
    isSaving.value = false
  }
}, 1000)

// Watch for changes in the form name and trigger debounced update
watch(() => form.name, (newName) => {
  debouncedUpdateCollectionName(newName)
}, { deep: true })
</script>

<template>
  <div>
    <div>
      <div class="flex items-center justify-between mt-2">
        <div class="text-xl font-bold">
          Edit Collection: {{ getLocaleContent(collection?.name || {}) }}
        </div>
      </div>

      <!-- Tab Content -->
      <div class="mt-6 space-y-8">
        <BaseCard>
          <BaseCardHeader>
            <BaseCardTitle>Collection Details</BaseCardTitle>
          </BaseCardHeader>
          <BaseCardContent>
            <div class="grid gap-6">
              <!-- Translatable Collection Name -->
              <FormTranslatableInput
                v-model="form.name"
                label="Collection Name"
                :required="true"
                :disabled="isSaving"
                placeholder="Enter a descriptive collection name..."
                help-text="This name will be used to identify and display the collection across different languages."
              />
            </div>

            <CollectionsProductSelector
              class="mt-6"
              :collection-id="collectionId"
            />
          </BaseCardContent>
        </BaseCard>
      </div>
    </div>
  </div>
</template>
