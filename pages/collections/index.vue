<script lang="ts" setup>
import { getLocaleContent } from '~/config/locales'

definePageMeta({
  middleware: ['auth'],
})

const tableSchema = [
  {
    key: 'name',
    label: 'Collection Name',
  },
  {
    key: 'productCount',
    label: 'Products',
  },
  {
    key: 'updatedAt',
    label: 'Updated',
  },
]

const { getDefaultHeaders, clientFetch } = useCustomFetch()
const { notifySuccess, notifyError } = useToast()
const route = useRoute()
const { setQueryParam } = useQueryParams()

// Pagination params
const routeQuery = computed(() => route.query)

// Fetch collections with pagination
const { data: pagination, refresh } = await useAsyncData(
  'collections',
  async () => {
    try {
      const { data } = await $fetch<ResponseData<Pagination<CollectionListItem[]>>>('/api/collections/listing', {
        headers: getDefaultHeaders(),
        params: {
          page: routeQuery.value.page,
          per_page: routeQuery.value.per_page,
          q: routeQuery.value.q,
        },
      })
      return data
    }
    catch (error) {
      console.error('Failed to fetch collections:', error)
      return null
    }
  },
  {
    watch: [routeQuery],
  },
)

const collections = computed(() => pagination.value?.data || [])

// Delete collection with optimistic UI update
const deletingIds = ref<number[]>([])
const optimisticCollections = computed(() => {
  if (!collections.value)
    return []
  return collections.value.filter(collection => !deletingIds.value.includes(collection.id))
})

async function doDelete(item: CollectionListItem) {
  // Optimistically remove from UI
  deletingIds.value.push(item.id)

  try {
    const success = await clientFetch<ResponseData<boolean>>(`/api/collections/${item.id}`, {
      method: 'DELETE',
    })

    if (success) {
      notifySuccess('Collection deleted successfully')
      // Refresh data after successful deletion
      refresh()
    }
    else {
      // Roll back on failure
      notifyError('Failed to delete collection')
      deletingIds.value = deletingIds.value.filter(id => id !== item.id)
    }
  }
  catch (error) {
    // Roll back on error
    console.error('Failed to delete collection:', error)
    notifyError('Failed to delete collection')
    deletingIds.value = deletingIds.value.filter(id => id !== item.id)
  }
}

// Format date for display
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString()
}

const searchQuery = ref(routeQuery.value.q as string || '')

const debouncedSearch = debounce(() => {
  setQueryParam({
    q: searchQuery.value,
    page: '1',
  })
}, 1000)
watch(() => searchQuery.value, () => {
  debouncedSearch()
})
</script>

<template>
  <div>
    <div class="flex justify-between items-center my-4">
      <h1 class="text-xl font-bold">
        Collections
      </h1>
    </div>
    <div>
      <SharedCollectionSearchOrCreate
        v-model:search-query="searchQuery"
        class="mb-3 max-w-md"
      />
    </div>

    <BaseCard>
      <BaseCardContent>
        <TableTemplate :columns="tableSchema" :data="optimisticCollections">
          <template #name="{ item }">
            <NuxtLink :to="`/collections/edit/${item.id}`" class="font-medium hover:underline text-primary">
              {{ getLocaleContent(item.name) }}
            </NuxtLink>
          </template>

          <template #productCount="{ item }">
            <div class="flex items-center">
              <span class="font-medium ml-2">{{ item.products_count || 0 }}</span>
            </div>
          </template>

          <template #updatedAt="{ item }">
            {{ item.updated_at ? formatDate(item.updated_at) : '-' }}
          </template>

          <template #actions="{ item }">
            <TableButtonEdit :url="`/collections/edit/${item.id}`" />
            <TableButtonDelete @delete="() => doDelete(item)" />
          </template>
        </TableTemplate>

        <!-- Pagination -->
        <div v-if="pagination && pagination.total > 0" class="mt-6">
          <TablePagination
            :total="pagination?.total || 0"
            :current="Number(routeQuery.page || 1)"
          />
        </div>
      </BaseCardContent>
    </BaseCard>
  </div>
</template>
