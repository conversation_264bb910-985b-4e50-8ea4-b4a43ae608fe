<script setup lang="ts">
const props = defineProps({
  perPage: {
    type: Number,
    required: true,
  },
})

const { setQueryParam } = useQueryParams()

function onChangePerPage(page: string) {
  if (page !== `${props.perPage}`) {
    setQueryParam({
      per_page: page,
      page: '1',
    })
  }
}
</script>

<template>
  <div class="flex items-center space-x-2">
    <p class="text-sm font-medium">
      Rows per page
    </p>
    <BaseSelect
      :model-value="`${perPage}`"
      @update:model-value="($event) => { onChangePerPage($event as string) }"
    >
      <BaseSelectTrigger class="h-8 w-[70px]">
        <BaseSelectValue :placeholder="`${perPage}`" />
      </BaseSelectTrigger>
      <BaseSelectContent side="top">
        <BaseSelectItem
          v-for="pageSize in [15, 30, 50, 100, 200, 500]"
          :key="pageSize"
          :value="`${pageSize}`"
        >
          {{ pageSize }}
        </BaseSelectItem>
      </BaseSelectContent>
    </BaseSelect>
  </div>
</template>
