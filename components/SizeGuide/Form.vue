<script lang="ts" setup>
import type { TableForm } from '@/types/size-guide'
import { Plus, Save, Trash2, X } from 'lucide-vue-next'
import { nextTick, ref } from 'vue'

const table = defineModel('table', {
  required: true,
  type: Object as PropType<TableForm>,
})

const editingCell = ref({ rowIndex: -1, cellIndex: -1 })
const editValue = ref('')

// Function to generate a unique ID for each cell input
function getCellInputId(rowIndex: number, cellIndex: number) {
  return `cell-input-${rowIndex}-${cellIndex}`
}

// Start editing a cell
function startEditing(rowIndex: number, cellIndex: number, initialValue: string) {
  editingCell.value = { rowIndex, cellIndex }
  editValue.value = initialValue

  // Use nextTick to ensure the DOM has updated before trying to focus
  nextTick(() => {
    const inputElement = document.getElementById(getCellInputId(rowIndex, cellIndex))
    if (inputElement) {
      inputElement.focus()
    }
  })
}

// Save the edited value
function saveEdit() {
  if (editingCell.value.rowIndex !== -1) {
    // If editing a header (rowIndex === 999)
    if (editingCell.value.rowIndex === 999) {
      const newHeaders = [...table.value.headers]
      newHeaders[editingCell.value.cellIndex] = editValue.value
      table.value = {
        ...table.value,
        headers: newHeaders,
      }
    }
    else {
      const newRows = [...table.value.rows]

      // If editing the row name
      if (editingCell.value.cellIndex === -1) {
        newRows[editingCell.value.rowIndex].name = editValue.value
      }
      else {
        // If editing a cell value
        newRows[editingCell.value.rowIndex].cells[editingCell.value.cellIndex] = editValue.value
      }

      table.value = {
        ...table.value,
        rows: newRows,
      }
    }
    editingCell.value = { rowIndex: -1, cellIndex: -1 }
  }
}

function cancelEdit() {
  editingCell.value = { rowIndex: -1, cellIndex: -1 }
}

function handleKeyPress(e: KeyboardEvent) {
  if (e.key === 'Enter') {
    saveEdit()
  }
  else if (e.key === 'Escape') {
    cancelEdit()
  }
}

// Add a new column
function addColumn() {
  const newHeaders = [...table.value.headers]
  newHeaders.push(newHeaders[newHeaders.length - 1])
  const newRows = table.value.rows.map(row => ({
    ...row,
    cells: [...row.cells, row.cells[row.cells.length - 1]],
  }))
  table.value = {
    ...table.value,
    headers: newHeaders,
    rows: newRows,
  }
}

// Delete a column
function deleteColumn(columnIndex: number) {
  // Don't delete the first column (row names)
  if (columnIndex === 0)
    return

  const adjustedIndex = columnIndex - 1 // Adjust for the row name column

  // Remove the column header
  const newHeaders = table.value.headers.filter((_, index) => index !== columnIndex)

  // Remove the corresponding cell from each row
  const newRows = table.value.rows.map(row => ({
    ...row,
    cells: row.cells.filter((_, index) => index !== adjustedIndex),
  }))

  table.value = {
    ...table.value,
    headers: newHeaders,
    rows: newRows,
  }
}

// Add a new row
function addRow() {
  const newRows = [...table.value.rows]
  newRows.push({
    ...newRows[newRows.length - 1],
    id: newRows.length + 1,
  })
  table.value = {
    ...table.value,
    rows: newRows,
  }
}

// Delete a row
function deleteRow(rowIndex: number) {
  const newRows = table.value.rows.filter((_, index) => index !== rowIndex)
  table.value = {
    ...table.value,
    rows: newRows,
  }
}
</script>

<template>
  <div class="w-full space-y-4">
    <div class="flex justify-between items-center">
      <h2 class="text-lg font-medium">
        Size Guide <span class="text-sm text-muted-foreground">(in INCHES)</span>
      </h2>
      <div class="flex gap-2">
        <BaseButton variant="outline" class="flex items-center gap-1" @click="addRow">
          <Plus class="h-4 w-4" /> Add Row
        </BaseButton>
        <BaseButton variant="outline" class="flex items-center gap-1" @click="addColumn">
          <Plus class="h-4 w-4" /> Add Column
        </BaseButton>
      </div>
    </div>

    <div class="border rounded-md overflow-auto w-fit">
      <BaseTable class="w-fit">
        <thead>
          <tr>
            <th class="relative min-w-[100px] text-left p-2 border-b">
              Size
            </th>
            <th v-for="(header, idx) in table.headers" :key="idx" class="relative min-w-[100px] text-left p-2 border-b">
              <template v-if="editingCell.rowIndex === 999 && editingCell.cellIndex === idx">
                <div class="flex items-center">
                  <input
                    :id="getCellInputId(999, idx)"
                    v-model="editValue"
                    class="w-32 border-b focus:outline-none"
                    @keydown="handleKeyPress"
                  >
                  <div class="absolute right-2 flex gap-1">
                    <BaseButton size="icon" variant="ghost" class="h-6 w-6" @click="saveEdit">
                      <Save class="h-3 w-3" />
                    </BaseButton>
                    <BaseButton size="icon" variant="ghost" class="h-6 w-6" @click="cancelEdit">
                      <X class="h-3 w-3" />
                    </BaseButton>
                  </div>
                </div>
              </template>
              <template v-else>
                <div class="flex items-center cursor-pointer hover:bg-muted/50 rounded" @click="startEditing(999, idx, header)">
                  <span>{{ header }}</span>
                  <BaseButton size="icon" variant="ghost" class="h-5 w-5 opacity-50 hover:opacity-100 mb-1" @click.stop="deleteColumn(idx)">
                    <Trash2 class="h-4 w-4" />
                  </BaseButton>
                </div>
              </template>
            </th>
            <th />
          </tr>
        </thead>
        <tbody>
          <tr v-for="(row, rowIndex) in table.rows" :key="row.id" class="border-b">
            <!-- Row name cell -->
            <td class="font-medium relative p-2">
              <template v-if="editingCell.rowIndex === rowIndex && editingCell.cellIndex === -1">
                <div class="flex items-center">
                  <input
                    :id="getCellInputId(rowIndex, -1)"
                    v-model="editValue"
                    class="w-40 border-b border-r-0 border-t-0 border-l-0 focus:outline-none"
                    @keydown="handleKeyPress"
                  >
                  <div class="absolute right-2 flex gap-1">
                    <BaseButton size="icon" variant="ghost" class="h-6 w-6" @click="saveEdit">
                      <Save class="h-3 w-3" />
                    </BaseButton>
                    <BaseButton size="icon" variant="ghost" class="h-6 w-6" @click="cancelEdit">
                      <X class="h-3 w-3" />
                    </BaseButton>
                  </div>
                </div>
              </template>
              <template v-else>
                <div
                  class="flex items-center justify-between cursor-pointer hover:bg-muted/50 rounded w-40 h-full"
                  @click="startEditing(rowIndex, -1, row.name)"
                >
                  <span>{{ row.name }}</span>
                </div>
              </template>
            </td>

            <!-- Data cells -->
            <td v-for="(cell, cellIndex) in row.cells" :key="cellIndex" class="relative">
              <template v-if="editingCell.rowIndex === rowIndex && editingCell.cellIndex === cellIndex">
                <div class="flex items-center">
                  <input
                    :id="getCellInputId(rowIndex, cellIndex)"
                    v-model="editValue"
                    class="w-16 border-b border-r-0 border-t-0 border-l-0 focus:outline-none"
                    @keydown="handleKeyPress"
                  >
                  <div class="absolute right-2 flex gap-1">
                    <BaseButton size="icon" variant="ghost" class="h-6 w-6" @click="saveEdit">
                      <Save class="h-3 w-3" />
                    </BaseButton>
                    <BaseButton size="icon" variant="ghost" class="h-6 w-6" @click="cancelEdit">
                      <X class="h-3 w-3" />
                    </BaseButton>
                  </div>
                </div>
              </template>
              <template v-else>
                <div
                  class="w-full h-full p-1 cursor-pointer hover:bg-muted/50 rounded"
                  @click="startEditing(rowIndex, cellIndex, cell)"
                >
                  {{ cell }}
                </div>
              </template>
            </td>
            <td>
              <BaseButton size="icon" variant="ghost" class="h-6 w-6" @click="deleteRow(rowIndex)">
                <Trash2 class="h-4 w-4" />
              </BaseButton>
            </td>
          </tr>
        </tbody>
      </BaseTable>
    </div>
  </div>
</template>
