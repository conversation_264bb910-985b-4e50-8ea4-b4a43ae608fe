<script lang="ts" setup>
import { Save } from 'lucide-vue-next'
import useEvents from '~/composables/custom/useEvents'

definePageMeta({
  middleware: ['auth'],
})

const router = useRouter()
const { clientFetch } = useCustomFetch()
const { notifyError, notifySuccess } = useToast()

const {
  form,
  validate,
  errors,
  initializeForm,
  generateSlugFromTitle,
} = useEvents()

// Initialize form with one empty row
onMounted(() => {
  initializeForm()
})

const isSaving = ref(false)

async function submit() {
  if (!validate()) {
    notifyError('Please fill in all required fields.')
    return
  }

  isSaving.value = true
  try {
    const success = await clientFetch('/api/event-pages', {
      method: 'POST',
      body: toRaw(form),
    })

    if (success) {
      notifySuccess('Event created successfully')
      router.push('/events')
    }
  }
  catch (error) {
    notifyError('Failed to create event')
    console.error('Error creating event:', error)
  }
  finally {
    isSaving.value = false
  }
}
</script>

<template>
  <div class="pt-1">
    <div class="flex items-center justify-between mb-6">
      <GoBack />
      <BaseButton
        :disabled="isSaving"
        size="sm"
        @click="submit"
      >
        <Save class="h-4 w-4" />
        {{ isSaving ? 'Saving...' : 'Save Event' }}
      </BaseButton>
    </div>

    <BaseCard>
      <BaseCardHeader>
        <BaseCardTitle>
          Create New Event
        </BaseCardTitle>
      </BaseCardHeader>
      <BaseCardContent>
        <div class="space-y-6">
          <!-- Basic Event Information -->
          <div class="grid gap-4">
            <FormInput
              id="event-title"
              v-model="form.title"
              label="Event Title"
              placeholder="Enter event title"
              required
              :error="errors.title"
              @input="generateSlugFromTitle"
            />

            <FormInput
              id="event-slug"
              v-model="form.slug"
              label="Event Slug"
              placeholder="event-slug"
              required
              :error="errors.slug"
              help-text="URL-friendly version of the title. Only lowercase letters, numbers, and hyphens."
            />

            <div class="flex items-center space-x-2">
              <BaseSwitch
                id="event-active"
                v-model="form.is_active"
              />
              <label for="event-active" class="text-sm font-medium">
                Active
              </label>
            </div>
          </div>

          <!-- Event Content Builder -->
          <div class="mt-8">
            <h3 class="text-lg font-medium mb-4">
              Event Content
            </h3>
            <EventsContentBuilder v-model="form.event_rows" />
          </div>
        </div>
      </BaseCardContent>
    </BaseCard>
  </div>
</template>
