export function getPriceLocation(product: ProductItem | ProductListItem, location: string = 'Vietnam', field: 'price' | 'compare_price' = 'price') {
  return product.prices_by_location.find(price => price.location === location)?.[field]
    || product[field] // fallback to default location
    || 0
}

export function getPriceString(product: ProductItem | ProductListItem, location: string = 'Vietnam', field: 'price' | 'compare_price' = 'price') {
  const hasLocation = product.prices_by_location.some(price => price.location === location)
  if (!hasLocation) {
    return `${getPriceLocation(product, location, field).toFixed(2)} USD`
  }

  switch (location) {
    case 'Vietnam':
      return `${new Intl.NumberFormat('en-US').format(getPriceLocation(product, location, field))} VNĐ`
    default:
      return `${getPriceLocation(product, location, field).toFixed(2)} USD`
  }
}
