<script setup lang="ts">
import { Badge } from '~/components/ui/badge'
import { computed } from 'vue'

interface Props {
  quantity: number
  lowStockThreshold?: number
}

const props = withDefaults(defineProps<Props>(), {
  lowStockThreshold: 5
})

const stockStatus = computed(() => {
  if (props.quantity === 0) {
    return {
      label: 'Out of Stock',
      variant: 'destructive' as const,
      class: 'bg-red-100 text-red-800 hover:bg-red-100'
    }
  } else if (props.quantity <= props.lowStockThreshold) {
    return {
      label: 'Low Stock',
      variant: 'secondary' as const,
      class: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100'
    }
  } else {
    return {
      label: 'In Stock',
      variant: 'default' as const,
      class: 'bg-green-100 text-green-800 hover:bg-green-100'
    }
  }
})
</script>

<template>
  <Badge 
    :variant="stockStatus.variant"
    :class="stockStatus.class"
  >
    {{ stockStatus.label }} ({{ quantity }})
  </Badge>
</template>
