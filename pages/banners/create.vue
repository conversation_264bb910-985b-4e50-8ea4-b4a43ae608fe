<script lang="ts" setup>
import useBanner from '@/composables/custom/banner'

definePageMeta({
  middleware: ['auth'],
})

const router = useRouter()

const { clientFetch } = useCustomFetch()
const { notifyError, notifySuccess } = useToast()
const { initBanner } = useBanner()

const { form } = useForm({
  banners: {
    default: [initBanner()],
    rules: [],
  },
})

async function submit() {
  const result = await clientFetch('/api/banners', {
    method: 'POST',
    body: toRaw(form),
  })

  if (result) {
    notifySuccess('Banner created successfully')
    router.push('/banners')
  }
  else {
    notifyError('Failed to create banner')
  }
}
</script>

<template>
  <div>
    <div class="flex items-center justify-between mt-2">
      <div class="text-lg uppercase">
        Create Banner
      </div>
      <BaseButton @click="submit">
        Save Changes
      </BaseButton>
    </div>
    <BaseCard class="mt-4">
      <BaseCardHeader>
        Banners
      </BaseCardHeader>
      <BaseCardContent>
        <BannerForm v-model:form="form" />
      </BaseCardContent>
    </BaseCard>
  </div>
</template>
