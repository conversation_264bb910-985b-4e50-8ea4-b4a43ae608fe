import type { SaleEvent } from './event'

export interface Order {
  id: number
  access_token: string
  discount_code: string | null
  customer_id: number
  total_price: string
  discount_price: string
  currency: string
  phone: string
  address: string
  city: string
  state: string
  zip: string
  country: string
  status: string
  payment_method: string
  created_at: string
  updated_at: string
  customer: Customer
  order_products: OrderProduct[]
}

// "products": [
//   {
//       "id": 1,
//       "order_id": 1,
//       "product_id": 22,
//       "product": {
//           "id": 22,
//           "name": "Button Half Placket Knitted Dress",
//           "slug": "button-half-placket-knitted-dress",
//           "image": "products/22/oGXiEi0eIw.jpg"
//       },
//       "price": 400000,
//       "discount_price": 0,
//       "final_price": 400000,
//       "quantity": 1,
//       "total": 400000,
//       "total_discount": 0,
//       "options": [
//           {
//               "id": 49,
//               "name": "white",
//               "type": "color",
//               "value": "#FFFFFF"
//           },
//           {
//               "id": 102,
//               "name": "XS",
//               "type": "size",
//               "value": "xs"
//           }
//       ],
//       "created_at": "2025-05-03T03:14:30.000000Z",
//       "updated_at": "2025-05-03T13:47:46.000000Z",
//       "currency": "VND"
//   }
// ],

export interface OrderProductDetail {
  id: number
  order_id: number
  product_id: number
  price: string
  discount_price: string
  currency: string
  quantity: number
  total: string
  total_discount: string
  options: ProductOptions[]
  created_at: string
  updated_at: string
  product: Product
}

export interface OrderProduct {
  id: number
  order_id: number
  product_id: number
  sale_event_id?: number
  sale_percent?: string
  price: string
  sale_price?: string
  discount_price: string
  currency: string
  quantity: number
  options: ProductOptions[]
  created_at: string
  updated_at: string
  product: ShortProduct
  sale_event?: SaleEvent
}

interface ProductOptions {
  id: number
  name: string
  type: string
  value: string
}

interface Product {
  id: number
  category_id: number
  sku: string
  slug: string
  name: string
  price: number
  compare_price: number
  options: ProductOptions
  status: number
  description: string | null
  prices_by_location: PriceByLocation[]
  image: FileDB
}
