<script lang="ts" setup>
import useProducts from '@/composables/custom/products'

definePageMeta({
  middleware: ['auth'],
})

const router = useRouter()
const route = useRoute()
const { clientFetch, getDefaultHeaders } = useCustomFetch()
const { notifyError, notifySuccess } = useToast()
const id = route.params.id as string
const finishInit = ref(false)
const { isSlugValid, validate, form, errors, isCheckingSlug, isCheckingSku, isSkuValid, setForm } = useProducts(finishInit)
const oldSlug = ref<string | null>()
const oldSku = ref<string | null>()

const { data } = await useAsyncData(`product_${route.params.id}`, async () => {
  const { data } = await $fetch<ResponseData<ProductItem>>(`/api/products/${route.params.id}`, {
    headers: getDefaultHeaders(),
  })

  return data
})

if (data.value) {
  setForm({
    ...data.value,
    status: <PERSON><PERSON>an(data.value.status),
    collections: data.value.collections.map(collection => collection.name),
    images: data.value.images.map(image => ({
      id: image.id,
      url: image.path,
      type: image.type,
    })),
    locationPrices: data.value.prices_by_location.map(price => ({
      country: price.location,
      price: price.price,
      compare_price: price.compare_price,
      id: price.location_id,
    })),
    size_guide_id: data.value.size_guide_id,
  })
  oldSlug.value = data.value.slug
  oldSku.value = data.value.sku
  setTimeout(() => {
    finishInit.value = true
  }, 0)
}

async function submit() {
  if (!form.price) {
    notifyError('Default price is required')
    return
  }

  if (!isSkuValid.value) {
    notifyError('SKU is not unique')
    return
  }

  if (!isSlugValid.value) {
    notifyError('Slug is not unique')
    return
  }

  if (validate()) {
    const result = await clientFetch(`/api/products/${id}`, {
      method: 'PUT',
      body: toRaw(form),
    })

    if (result) {
      notifySuccess('Product update successfully')
      router.push('/products')
    }
  }
}
</script>

<template>
  <div>
    <div class="flex items-center justify-between mt-2">
      <div class="text-lg uppercase">
        Edit Product
      </div>
      <BaseButton @click="submit">
        Save Changes
      </BaseButton>
    </div>
    <div class="grid grid-cols-12 mt-2 gap-6">
      <div class="col-span-8 space-y-6">
        <ProductsFormInfo
          v-model:form="form"
          :errors="errors"
          :is-checking-slug="isCheckingSlug"
          :is-slug-valid="isSlugValid"
          :is-checking-sku="isCheckingSku"
          :is-sku-valid="isSkuValid"
          :has-slug-update="Boolean(oldSlug && oldSlug !== form.slug)"
          :has-sku-update="Boolean(oldSku && oldSku !== form.sku)"
        />
        <ProductsFormOptions v-model:options="form.options" />
        <ProductsFormPrice
          v-model:location-prices="form.locationPrices"
          v-model:compare-price="form.compare_price"
          v-model:price="form.price"
        />
      </div>
      <BaseCard class="col-span-4">
        <BaseCardHeader>
          Images
        </BaseCardHeader>
        <BaseCardContent>
          <FormUploadMultiImage
            v-model="form.images"
            :error="errors.images"
          />
        </BaseCardContent>
      </BaseCard>
    </div>
  </div>
</template>
