<script lang="ts" setup>
interface Props {
  errors: any
  isCheckingSlug: boolean
  isSlugValid: boolean
  isCheckingSku: boolean
  isSkuValid: boolean
  hasSlugUpdate?: boolean
  hasSkuUpdate?: boolean
}
withDefaults(defineProps<Props>(), {
  hasSlugUpdate: true,
  hasSkuUpdate: true,
})

const form = defineModel('form', {
  type: Object,
  required: true,
})
</script>

<template>
  <BaseCard>
    <BaseCardHeader>
      Product Info
    </BaseCardHeader>
    <BaseCardContent>
      <div class="space-y-4 pl-2 pr-8">
        <div>
          <FormInput
            id="sku"
            v-model="form.sku"
            :error="errors.sku"
            label="SKU"
            placeholder="SKU"
          />
          <div v-if="form.sku && hasSkuUpdate" class="text-xs mt-1">
            <span v-if="isCheckingSku">Checking SKU...</span>
            <span v-else-if="!isSkuValid" class="text-red-600">SKU is not unique</span>
            <span v-else class="text-green-600">SKU valid!</span>
          </div>
        </div>
        <FormInput
          id="name"
          v-model="form.name"
          :error="errors.name"
          label="Name"
          placeholder="Name"
        />
        <div>
          <FormInput
            id="slug"
            v-model="form.slug"
            :error="errors.slug"
            label="Slug"
            placeholder="Slug"
          />
          <div v-if="form.slug && hasSlugUpdate" class="text-xs mt-1">
            <span v-if="isCheckingSlug">Checking Slug...</span>
            <span v-else-if="!isSlugValid" class="text-red-600">Slug is not unique</span>
            <span v-else class="text-green-600">Slug valid!</span>
          </div>
        </div>
        <CustomCollectionInput v-model="form.collections" />
        <ProductsSizeGuideSelect v-model="form.size_guide_id" />
        <FormEditor
          id="description"
          v-model="form.description"
          label="Description"
          type="text"
        />
      </div>
    </BaseCardContent>
  </BaseCard>
</template>
