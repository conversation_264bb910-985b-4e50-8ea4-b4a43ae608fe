<script setup lang="ts">
import { useVModel } from '@vueuse/core'

interface Option {
  id: number
  name: string
}

const props = defineProps<{
  modelValue: number[]
  options: Option[]
  label: string
}>()
const emit = defineEmits(['update:modelValue'])
const current = useVModel(props, 'modelValue', emit)

function toggle(id: number) {
  const index = current.value.indexOf(id)
  if (index === -1) {
    current.value.push(id)
  }
  else {
    current.value.splice(index, 1)
  }
}
</script>

<template>
  <div>
    <BaseLabel>
      {{ label }}
    </BaseLabel>
    <div class="space-y-2 mt-1">
      <div
        v-for="option in options"
        :key="option.id"
        class="flex items-center space-x-1"
      >
        <BaseCheckbox
          :id="`checkbox-${option.id}`"
          :checked="current.includes(option.id)"
          @update:checked="toggle(option.id)"
        />
        <BaseLabel
          :for="`checkbox-${option.id}`"
          class="text-sm font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          {{ option.name }}
        </BaseLabel>
      </div>
    </div>
  </div>
</template>
