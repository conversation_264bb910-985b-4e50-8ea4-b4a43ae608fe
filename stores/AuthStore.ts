export const useAuthStore = defineStore('auth', {
  state: () => ({
    token: null as string | null,
    user: null,
    permissions: [] as string[],
    role: null as string | null,
  } as AuthState),
  getters: {
    hasPermission: state => (permission: string) => {
      return state.permissions.includes(permission)
    },
    hasInit: (state) => {
      return state.token !== null && state.user !== null
    },
  },
  actions: {
    initialize({ token, user, permissions, role }: AuthState) {
      this.token = token
      this.user = user
      this.permissions = permissions
      this.role = role
    },

    logout() {
      this.token = null
      this.user = null
      this.permissions = []
    },
  },
})
