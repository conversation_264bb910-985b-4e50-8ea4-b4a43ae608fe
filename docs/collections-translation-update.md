# Collections Translation Support

Collection names now support multiple languages using the same beautiful translatable interface as blog titles.

## ✅ **What Was Updated**

### **Type Definitions**

- **`types/collection.d.ts`**: Updated `Collection.name` to support both `string | LocaleContent` for backward compatibility

### **Pages**

- **`pages/collections/edit/[id].vue`**: Complete redesign using `FormTranslatableInput` component
- **`pages/collections/index.vue`**: Already supported LocaleContent (no changes needed)

### **Components**

- **`components/Shared/CollectionSearchOrCreate.vue`**: Now creates collections with LocaleContent format
- **`components/custom/CollectionInput.vue`**: Updated to display translatable names properly

## 🎯 **New Features**

### **Modern Translation Interface**

- **Card-based design** with visual hierarchy
- **Progress tracking** showing translation completion
- **Status indicators** for each locale (✓ ○ ⚠️)
- **Smart validation** and real-time feedback
- **Contextual help** explaining fallback behavior

### **Automatic Debounced Saving**

- **Auto-save** after 1 second of inactivity
- **Smart validation** (minimum 3 characters for default locale)
- **Error handling** with proper user feedback
- **Loading states** integrated into the component

### **Backward Compatibility**

- **Seamless migration** from string to LocaleContent format
- **Fallback display** using `getLocaleContent()` helper
- **API compatibility** with both formats

## 🖼️ **Visual Improvements**

**Before:**

```html
<!-- Old, basic input -->
<label>Name *</label>
<BaseInput v-model="collection.name" />
<Loader v-if="isSaving" />
```

**After:**

```html
<!-- New, beautiful translatable input -->
<FormTranslatableInput
  v-model="form.name"
  label="Collection Name"
  :required="true"
  :disabled="isSaving"
  placeholder="Enter a descriptive collection name..."
  help-text="This name will be used to identify and display the collection across different languages."
/>
```

## 📊 **Translation Status Display**

The new interface shows:

- **Progress bar**: Visual completion percentage
- **Tab indicators**: Icons showing which locales have content
- **Smart badges**: "Required" indicator for default locale
- **Status overview**: Quick glance at all translation states

## 💾 **Data Format**

**Old Format (String):**

```json
{
  "id": 1,
  "name": "Summer Collection",
  "slug": "summer-collection"
}
```

**New Format (LocaleContent):**

```json
{
  "id": 1,
  "name": {
    "en": "Summer Collection",
    "vi": "Bộ Sưu Tập Mùa Hè",
    "ru": "Летняя Коллекция",
    "zn": "夏季系列"
  },
  "slug": "summer-collection"
}
```

## 🔄 **Migration Handling**

The system automatically handles migration:

```typescript
// Auto-converts string to LocaleContent
function normalizeCollectionName(name: string | LocaleContent | undefined): LocaleContent {
  if (!name) {
    return createEmptyLocaleContent() as LocaleContent
  }

  if (typeof name === 'string') {
    // Convert old string format to LocaleContent format
    const content = createEmptyLocaleContent()
    content[DEFAULT_LOCALE] = name
    return content as LocaleContent
  }

  return name
}
```

## 🎯 **Usage Examples**

### **Creating New Collections**

When users create collections via search/create component:

```typescript
// Auto-creates LocaleContent format
const nameContent = createEmptyLocaleContent()
nameContent[DEFAULT_LOCALE] = searchQuery.value.trim()

await clientFetch('/api/collections', {
  method: 'POST',
  body: { name: nameContent }
})
```

### **Displaying Collections**

All collection displays automatically use fallback logic:

```html
<!-- Automatically shows best available translation -->
<span>{{ getLocaleContent(collection.name) }}</span>
```

### **Form Integration**

```html
<!-- Product form with collection tags -->
<FormInputTags :predefined-tags="collections.map(c => getLocaleContent(c.name))" />
```

## 🌍 **Benefits**

- **🎨 Beautiful UX**: Modern, professional interface
- **🚀 Easy to Use**: Intuitive translation workflow
- **⚡ Auto-Save**: No lost work with debounced saving
- **🔄 Backward Compatible**: Works with existing data
- **📱 Responsive**: Works perfectly on all devices
- **🎯 Type Safe**: Full TypeScript support
- **🌐 Future Proof**: Automatically supports new locales

## 🔧 **Backend Considerations**

When updating your Laravel backend:

1. **Update Model**: Add `name` to `$translatable` array
2. **Database**: Ensure `name` column is `json` type
3. **Validation**: Handle both string and object validation
4. **API Responses**: Return proper LocaleContent format

```php
// Laravel Model
class Collection extends Model
{
    use HasTranslations;

    protected $translatable = ['name'];
    protected $fillable = ['name', 'slug'];
}
```

## 🎉 **Result**

Collections now have the same beautiful, professional translation interface as blog titles! Users can easily manage collection names in multiple languages with clear visual feedback and smart fallback behavior.
