<script setup lang="ts">
import { useSidebar } from './utils'

const { toggleSidebar } = useSidebar()
</script>

<template>
  <BaseSidebarMenuButton
    size="lg"
    class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
    @click="toggleSidebar"
  >
    <div class="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
      <img src="/logo.jpg" alt="Team logo">
    </div>
    <div class="grid flex-1 text-left text-sm leading-tight">
      <span class="truncate font-semibold">DEZUS COMPANY</span>
      <span class="truncate text-xs">Enterprise</span>
    </div>
  </BaseSidebarMenuButton>
</template>
