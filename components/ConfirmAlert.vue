<script setup lang="ts">
import { useEventBus } from '@vueuse/core'
import { AlertCircle, Check, X } from 'lucide-vue-next'

const bus = useEventBus<string>('confirm-alert')
const isOpen = ref(false)
const titleRef = ref('')
const descriptionRef = ref('')
const callback = ref<() => void>()
bus.on((event: string, { description, title, onConfirm }: any) => {
  switch (event) {
    case 'show-confirm':
      isOpen.value = true
      titleRef.value = title
      descriptionRef.value = description
      callback.value = onConfirm
      break

    default:
      break
  }
})

function handleConfirm() {
  if (callback.value) {
    callback.value()
  }
  isOpen.value = false
}

function handleCancel() {
  isOpen.value = false
}
</script>

<template>
  <div>
    <div v-if="isOpen" class="fixed inset-0 flex items-center justify-center bg-black/50 backdrop-blur-sm z-[9999]">
      <div class="w-full max-w-md p-4 animate-in fade-in zoom-in duration-300">
        <BaseCard class="border-0 shadow-lg">
          <BaseCardHeader class="bg-gradient-to-r from-pink-100 to-purple-100 rounded-t-lg">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <div class="rounded-full bg-pink-500 p-1.5">
                  <AlertCircle class="h-5 w-5 text-white" />
                </div>
                <div>{{ titleRef }}</div>
              </div>
              <BaseButton
                variant="ghost"
                size="icon"
                class="rounded-full h-8 w-8 hover:bg-pink-200/50"
                @click="handleCancel"
              >
                <X class="h-4 w-4" />
              </BaseButton>
            </div>
          </BaseCardHeader>
          <BaseCardContent class="pt-6 pb-4">
            <p class="text-gray-600">
              {{ descriptionRef }}
            </p>
          </BaseCardContent>
          <BaseCardContent class="flex justify-end gap-3 border-t pt-4">
            <BaseButton
              variant="outline"
              class="border-gray-200 hover:bg-gray-100 hover:text-gray-800"
              @click="handleCancel"
            >
              Cancel
            </BaseButton>
            <BaseButton
              class="bg-gradient-to-r from-pink-500 to-purple-500 text-white hover:from-pink-600 hover:to-purple-600"
              @click="handleConfirm"
            >
              <Check class="mr-2 h-4 w-4" /> Confirm
            </BaseButton>
          </BaseCardContent>
        </BaseCard>
      </div>
    </div>
  </div>
</template>
