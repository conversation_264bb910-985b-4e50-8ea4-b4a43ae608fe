import type { SizeGuide } from '~/types/size-guide'
import { defineStore } from 'pinia'

interface Settings {
  colors: Option[]
  sizes: Option[]
  locations: Location[]
  collections: Collection[]
  sizeGuides: Omit<SizeGuide, 'headers' | 'rows'>[]
  isInitialized: boolean
}

export const useSettingsStore = defineStore('settings', {
  state: (): Settings => ({
    colors: [],
    sizes: [],
    locations: [],
    collections: [],
    sizeGuides: [],
    isInitialized: false,
  }),
  actions: {
    async fetchSettings(force = false) {
      if (this.isInitialized && !force) {
        return
      }

      const { getDefaultHeaders } = useCustomFetch()
      const { data } = await useAsyncData('settings', async () => {
        const { data } = await $fetch<ResponseData<Settings>>('/api/settings', { headers: getDefaultHeaders() })
        return data
      })

      if (data.value) {
        this.colors = Object.values(data.value.colors)
        this.sizes = data.value.sizes
        this.locations = data.value.locations
        this.collections = data.value.collections
        this.sizeGuides = data.value.sizeGuides
      }

      this.isInitialized = true
    },
  },
})
