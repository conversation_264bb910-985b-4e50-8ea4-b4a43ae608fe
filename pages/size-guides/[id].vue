<script lang="ts" setup>
import type { SizeGuide } from '~/types/size-guide'
import { useSizeGuides } from '~/composables/custom/sizeGuides'

definePageMeta({
  middleware: ['auth'],
})

const router = useRouter()
const { clientFetch, getDefaultHeaders } = useCustomFetch()
const { notifySuccess } = useToast()
const { form, validate, setForm } = useSizeGuides()

const id = useRoute().params.id as string
const { data: sizeGuide } = await useAsyncData(`size-guide-${id}`, async () => {
  const { data } = await $fetch<ResponseData<SizeGuide>>(`/api/size-guides/${id}`, {
    headers: getDefaultHeaders(),
  })

  return data
})

if (sizeGuide.value) {
  setForm({
    name: sizeGuide.value.name,
    table: {
      headers: sizeGuide.value.headers,
      rows: sizeGuide.value.rows,
    },
  })
}

async function submit() {
  if (!validate()) {
    return
  }

  const success = await clientFetch(`/api/size-guides/${id}`, {
    method: 'PUT',
    body: toRaw(form),
  })

  if (success) {
    notifySuccess('Size guide created successfully')
    router.push('/size-guides')
  }
}
</script>

<template>
  <div>
    <div class="flex items-center justify-between mt-2">
      <div class="text-lg uppercase">
        Edit Size Guide
      </div>
      <BaseButton @click="submit">
        Save Changes
      </BaseButton>
    </div>
    <BaseCard class="mt-4">
      <BaseCardHeader />
      <BaseCardContent>
        <div class="space-y-4">
          <FormInput
            id="size-guide-name"
            v-model="form.name"
            label="Name"
          />
          <SizeGuideForm
            v-model:table="form.table"
          />
        </div>
      </BaseCardContent>
    </BaseCard>
  </div>
</template>
