<script setup lang="ts">
const props = defineProps<{
  options: { label: string, value: string }[]
  paramKey: string
  label: string
  placeholder: string
  defaultValue?: string
}>()

const route = useRoute()

const selected = ref(route.query[props.paramKey] as string || props.defaultValue || '')
const { setQueryParam } = useQueryParams()

function handleChange(value: string) {
  setQueryParam({ [props.paramKey]: value })
}

watch(selected, (value) => {
  if (value === 'all') {
    setQueryParam({ [props.paramKey]: undefined })
    return
  }

  handleChange(value)
})

const currentLabel = computed(() => {
  // If no value is selected and we have a defaultValue, find the default option
  const currentValue = selected.value || props.defaultValue || ''
  const option = props.options.find(
    option => option.value.toString() === currentValue.toString(),
  )
  return option ? option.label : ''
})
</script>

<template>
  <BaseSelect v-model="selected">
    <BaseSelectTrigger :aria-label="placeholder">
      <BaseSelectValue :placeholder="placeholder">
        <span class="font-semibold pr-2">{{ label }}:</span><span>{{ currentLabel }}</span>
      </BaseSelectValue>
    </BaseSelectTrigger>
    <BaseSelectContent>
      <BaseSelectItem
        v-for="option in options"
        :key="option.value"
        :value="option.value.toString()"
      >
        {{ option.label }}
      </BaseSelectItem>
    </BaseSelectContent>
  </BaseSelect>
</template>
