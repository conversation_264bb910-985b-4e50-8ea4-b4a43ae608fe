type ValidationRule = (value: any, message?: string) => true | string
type ValidationSchema = Record<string, ValidationRule[]>

export function required(value: string, message?: string): true | string {
  return value ? true : message || 'This field is required'
}

export function email(value: string): true | string {
  return /^\S[^\s@]*@\S[^\s.]*\.\S+$/.test(value) ? true : 'Email invalid!'
}

export function minLength(min: number, message?: string) {
  return (value: string): true | string =>
    value.length >= min ? true : message || `Min length is ${min}`
}

export function maxLength(max: number, message?: string) {
  return (value: string): true | string =>
    value.length <= max ? true : message || `Max length is ${max}`
}

export function isPositiveNumber(value: number | string, message?: string): true | string {
  const num = typeof value === 'string' ? Number.parseFloat(value) : value
  return !Number.isNaN(num) && num > 0
    ? true
    : message || 'Must be a positive number'
}

export function isNonNegativeNumber(value: number | string, message?: string): true | string {
  const num = typeof value === 'string' ? Number.parseFloat(value) : value
  return !Number.isNaN(num) && num >= 0
    ? true
    : message || 'Must be a non-negative number'
}

// Usage example:
/*
const userSchema = validateObject({
  name: [required],
  email: [required, email],
  age: [required, isPositiveNumber],
  balance: [isNonNegativeNumber]
});

const user = {
  name: 'John',
  email: 'invalid-email',
  age: -5,
  balance: 100
};

const validationResult = userSchema(user);
// Returns:
// {
//   email: 'Email invalid!',
//   age: 'Must be a positive number'
// }
*/
export function validateObject(schema: ValidationSchema) {
  return (obj: Record<string, any>): true | Record<string, string> => {
    const errors: Record<string, string> = {}

    for (const [property, rules] of Object.entries(schema)) {
      for (const rule of rules) {
        const result = rule(obj[property])
        if (result !== true) {
          errors[property] = result
          break // Stop on first error for this property
        }
      }
    }

    return Object.keys(errors).length === 0 ? true : errors
  }
}
