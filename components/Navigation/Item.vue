<script setup lang="ts">
import { useEventBus } from '@vueuse/core'
import { Edit, GripVertical } from 'lucide-vue-next'
import { getLocaleContent } from '~/config/locales'

defineProps<{
  menuId: number
}>()
const emit = defineEmits(['refresh'])

interface Item {
  id: number
  title: string
  position: number
  total?: number
  slug?: string
  collections?: Array<{
    id: number
    title: string
    total: number
  }>
}

const items = defineModel('items', {
  type: Array as PropType<Item[]>,
  required: true,
})

const bus = useEventBus<string>('edit-navigation-item')
const { clientFetch } = useCustomFetch()

function onEditItem(item: Item) {
  bus.emit('edit-navigation-item', item)
}

async function onChangePosition(event: any) {
  const oldIndex = event.moved.oldIndex
  const target = items.value[oldIndex]
  const menu = event.moved.element
  const success = await clientFetch(`/api/menu-items/update-position/${menu.id}`, {
    method: 'PUT',
    body: {
      old_position: menu.position,
      new_position: target.position,
    },
  })

  if (success) {
    useToast().notifySuccess('Menu position updated successfully')
  }
  else {
    useToast().notifyError('Failed to update menu position')
    emit('refresh')
  }
}
</script>

<template>
  <draggable
    v-model="items"
    group="items"
    item-key="id"
    @change="onChangePosition"
  >
    <template #item="{ element: item }">
      <div class="mb-3 p-3 border rounded-lg bg-white shadow-sm hover:shadow-md transition-all duration-200">
        <div class="flex items-start justify-between">
          <div class="flex items-start gap-2 flex-1">
            <div class="mt-1 cursor-move">
              <GripVertical class="h-4 text-gray-400 w-4 hover:text-gray-600" />
            </div>

            <div class="flex-1">
              <div class="flex items-center gap-2 mb-2">
                <h4 class="font-medium text-gray-900">
                  {{ getLocaleContent(item.title) }}
                </h4>
              </div>

              <div class="flex items-center text-xs text-gray-500">
                <span class="bg-gray-100 px-2 py-1 rounded-md">
                  {{ item.total || 0 }} products
                </span>
              </div>
            </div>
          </div>

          <div class="flex items-center cursor-pointer ml-2">
            <Edit class="h-4 w-4 text-gray-400 hover:text-gray-600" @click="onEditItem(item)" />
          </div>
        </div>
      </div>
    </template>
  </draggable>
</template>
