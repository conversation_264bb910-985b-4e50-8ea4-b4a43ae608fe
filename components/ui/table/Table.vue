<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'

const props = defineProps<{
  class?: HTMLAttributes['class']
  maxHeight?: number
}>()

const style = computed(() => {
  const init = ''
  if (props.maxHeight) {
    return `max-height: ${props.maxHeight}px;`
  }
  return init
})
</script>

<template>
  <div class="relative w-full overflow-auto" :style="style">
    <table :class="cn('w-full caption-bottom text-sm', props.class)">
      <slot />
    </table>
  </div>
</template>
