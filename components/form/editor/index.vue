<script setup lang="ts">
import jsBeautify from 'js-beautify'

defineProps<{
  label: string
}>()
const content = defineModel('modelValue', {
  type: [String, null],
  required: true,
})

const rawContent = ref('')
const isRawMode = ref(false)
const isFocused = ref(false)

const editor = useEditor({
  content: content.value,
  extensions: [TiptapStarterKit],
  onUpdate: ({ editor }) => {
    content.value = editor.getHTML()
    if (!isRawMode.value) {
      rawContent.value = editor.getHTML()
    }
  },
  onFocus() {
    isFocused.value = true
  },
  onBlur() {
    isFocused.value = false
  },
})

function updateFromRaw() {
  if (isRawMode.value && editor.value) {
    editor.value.commands.setContent(rawContent.value)
  }
}

function toggleRawMode() {
  if (!editor.value)
    return

  if (!isRawMode.value) {
    const html = editor.value.getHTML()
    rawContent.value = jsBeautify.html(html, {
      indent_size: 2,
      indent_char: ' ',
      max_preserve_newlines: 2,
      wrap_line_length: 0,
    })
  }
  else {
    const minifiedHTML = minifyHTML(rawContent.value)
    editor.value.commands.setContent(minifiedHTML)
    content.value = minifiedHTML
  }
  isRawMode.value = !isRawMode.value
}

onBeforeUnmount(() => {
  unref(editor)?.destroy()
})
</script>

<template>
  <div>
    <BaseLabel class="text-nowrap">
      {{ label }}
    </BaseLabel>
    <div class="max-w-[600px] space-y-1">
      <FormEditorMenu v-if="editor" :editor="editor" :is-raw-mode="isRawMode" @toggle-raw="toggleRawMode" />
      <div v-if="isRawMode" class="min-h-32 border rounded border-input shadow-sm p-2">
        <textarea
          v-model="rawContent"
          class="w-full h-full min-h-24 border-none outline-none resize-none"
          @input="updateFromRaw"
        />
      </div>
      <TiptapEditorContent
        v-else-if="editor"
        class="min-h-32 border rounded border-input shadow-sm reverse-preflight p-2"
        :class="{ '!border-ring': isFocused }"
        :editor="editor"
        @click="editor.commands.focus()"
      />
    </div>
  </div>
</template>
