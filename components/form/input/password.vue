<script setup lang="ts">
import type { InputProps, VModelEmits } from '@/types/component'
import { Eye, EyeOff } from 'lucide-vue-next'
import { useInputLogic } from './logic'

const props = defineProps<InputProps>()
const emit = defineEmits<VModelEmits>()

const { innerValue, style, inputClass } = useInputLogic(props, emit)

const showHidden = ref(false)
function toggleVisibility() {
  showHidden.value = !showHidden.value
}
</script>

<template>
  <div class="grid gap-2">
    <BaseLabel :for="id" class="text-nowrap">
      {{ label }}
    </BaseLabel>
    <div class="relative">
      <BaseInput
        :id="id"
        v-model="innerValue"
        :style="style"
        :type="showHidden ? 'text' : 'password'"
        :placeholder="placeholder"
        :required="required"
        :class="inputClass"
      />
      <BaseButton
        type="button"
        variant="ghost"
        size="icon"
        class="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
        @click="toggleVisibility"
      >
        <EyeOff v-if="showHidden" class="h-4 w-4 text-muted-foreground" />
        <Eye v-else class="h-4 w-4 text-muted-foreground" />
        <span class="sr-only">{{ showHidden ? "Hide" : "Show" }}</span>
      </BaseButton>
    </div>
    <p v-if="error" class="text-red-500 text-sm">
      {{ error }}
    </p>
  </div>
</template>
