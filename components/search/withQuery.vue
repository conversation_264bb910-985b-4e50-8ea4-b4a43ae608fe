<script setup lang="ts">
import { Search } from 'lucide-vue-next'

defineProps<{
  placeholder?: string
}>()

const { setQueryParam } = useQueryParams()
const route = useRoute()
const search = ref(route.query.keyword as string || '')
function handleSubmit() {
  setQueryParam({ keyword: search.value })
}
</script>

<template>
  <form class="flex-1 sm:flex-initial" @submit.prevent="handleSubmit">
    <div class="relative">
      <Search class="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
      <BaseInput
        v-model="search"
        type="search"
        :placeholder="placeholder"
        class="pl-8 sm:w-[300px] md:w-[200px] lg:w-[300px]"
      />
    </div>
  </form>
</template>
