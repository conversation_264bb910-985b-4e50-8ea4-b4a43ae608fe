<script lang="ts" setup generic="T extends { id?: string | number }">
interface TableColumn {
  key: string | number
  label: string
}

const props = defineProps<{
  columns: TableColumn[]
  data?: T[] | null
  maxHeight?: number
}>()
</script>

<template>
  <BaseTable class="text-sm whitespace-nowrap table-auto" :max-height="maxHeight">
    <BaseTableHeader class="bg-background sticky top-0 z-10">
      <BaseTableRow>
        <BaseTableHead v-for="column in props.columns" :key="column.key" class="whitespace-nowrap px-4 py-2">
          {{ column.label }}
        </BaseTableHead>
        <BaseTableHead v-if="$slots.actions" class="whitespace-nowrap px-4 py-2">
          Actions
        </BaseTableHead>
      </BaseTableRow>
    </BaseTableHeader>

    <BaseTableBody v-if="data" class="divide-y divide-gray-200">
      <BaseTableRow v-for="item in props.data" :key="item.id ?? 'unk'" class="hover:bg-gray-50">
        <BaseTableCell v-for="column in props.columns" :key="column.key" class="whitespace-nowrap px-4 py-2">
          <slot :name="column.key" :item="item" />
        </BaseTableCell>
        <BaseTableCell v-if="$slots.actions" class="whitespace-nowrap px-4 py-2">
          <div class="flex gap-2">
            <slot name="actions" :item="item" />
          </div>
        </BaseTableCell>
      </BaseTableRow>
    </BaseTableBody>
    <div v-else class="text-center py-4">
      No data available
    </div>
  </BaseTable>
</template>
