<script setup lang="ts">
import { GripVertical, Languages, Trash2 } from 'lucide-vue-next'
import { ref } from 'vue'
import { createEmptyLocaleContent, DEFAULT_LOCALE, getLocaleContent } from '~/config/locales'

interface Column {
  id: number
  title: string | LocaleContent // Support both string and LocaleContent for backward compatibility
  total?: number
  slug?: string
}

defineEmits(['delete'])

const { clientFetch } = useCustomFetch()

const column = defineModel('column', {
  type: Object as PropType<Column>,
  required: true,
})

const isTranslating = ref(false)
const translatedTitle = ref<LocaleContent>({} as LocaleContent)

// Convert title to LocaleContent format for consistency
function normalizeTitle(title: string | LocaleContent | undefined): LocaleContent {
  if (!title) {
    return createEmptyLocaleContent() as LocaleContent
  }

  if (typeof title === 'string') {
    const content = createEmptyLocaleContent()
    content[DEFAULT_LOCALE] = title
    return content as LocaleContent
  }

  return title
}

function onTranslate() {
  isTranslating.value = true
  translatedTitle.value = normalizeTitle(column.value.title)
}

async function saveTranslations() {
  const success = await clientFetch(`/api/menu/${column.value.id}`, {
    method: 'PUT',
    body: {
      title: translatedTitle.value,
    },
  })

  if (success) {
    useToast().notifySuccess('Menu translations updated successfully')
    column.value.title = translatedTitle.value
    isTranslating.value = false
  }
}

function cancelTranslating() {
  isTranslating.value = false
}
</script>

<template>
  <div>
    <!-- Translation Modal -->
    <BaseDialog v-model:open="isTranslating">
      <BaseDialogContent class="sm:max-w-4xl">
        <BaseDialogHeader>
          <BaseDialogTitle>Edit Menu Translations</BaseDialogTitle>
        </BaseDialogHeader>
        <div class="py-4">
          <FormTranslatableInput
            v-model="translatedTitle"
            label="Menu Title"
            :required="true"
            placeholder="Enter menu title..."
            help-text="This title will be used in navigation menus across different languages."
          />
        </div>
        <BaseDialogFooter>
          <BaseButton variant="outline" @click="cancelTranslating">
            Cancel
          </BaseButton>
          <BaseButton @click="saveTranslations">
            Save Translations
          </BaseButton>
        </BaseDialogFooter>
      </BaseDialogContent>
    </BaseDialog>

    <!-- Main Header -->
    <div class="mb-2 flex items-center justify-between text-xl font-bold">
      <div class="gap-2 flex items-center">
        <div>
          <GripVertical class="h-4 text-muted-foreground w-4 cursor-move" />
        </div>
        <div class="flex flex-col">
          <h3 class="text-lg font-semibold text-gray-900">
            {{ getLocaleContent(column.title) }}
          </h3>
          <div class="flex items-center gap-2 mt-1">
            <span class="text-xs text-gray-500">{{ column.total || 0 }} products</span>
            <!-- Translation Status Indicator -->
            <span
              v-if="typeof column.title === 'object'"
              class="inline-flex items-center gap-1 px-1.5 py-0.5 rounded text-xs bg-green-100 text-green-700"
            >
              <Languages class="h-3 w-3" />
              Multilingual
            </span>
          </div>
        </div>
      </div>
      <div class="gap-1 flex items-center">
        <!-- Translate Button -->
        <BaseButton
          variant="ghost"
          size="sm"
          class="p-0 h-8 w-8 text-blue-600"
          title="Edit Translations"
          @click="onTranslate"
        >
          <Languages class="h-4 w-4" />
        </BaseButton>
      </div>
    </div>
  </div>
</template>
