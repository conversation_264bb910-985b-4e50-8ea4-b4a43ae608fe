<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import { Search } from 'lucide-vue-next'

const props = defineProps<{
  placeholder?: string
  modelValue: string
}>()
const emit = defineEmits(['update:modelValue'])
const search = useVModel(props, 'modelValue', emit)
</script>

<template>
  <form class="flex-1 sm:flex-initial">
    <div class="relative">
      <Search class="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
      <BaseInput
        v-model="search"
        type="search"
        :placeholder="placeholder"
        class="pl-8 sm:w-[300px] md:w-[200px] lg:w-[300px]"
      />
    </div>
  </form>
</template>
