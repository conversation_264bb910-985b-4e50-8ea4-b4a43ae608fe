<script setup lang="ts">
const current = defineModel('modelValue', {
  type: [Number, null],
  required: true,
})

const settingsStore = useSettingsStore()
const sizeGuides = computed(() => settingsStore.sizeGuides)
</script>

<template>
  <div>
    <BaseLabel class="text-nowrap">
      Size Guide
    </BaseLabel>
    <FormSelectWithSearch
      v-model="current"
      :options="sizeGuides.map(sizeGuide => ({
        label: sizeGuide.name,
        value: sizeGuide.id,
      }))"
      label="Size Guide"
      cs-placeholder="Select a size guide"
    />
  </div>
</template>
