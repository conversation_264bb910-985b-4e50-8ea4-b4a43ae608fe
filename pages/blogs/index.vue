<script setup lang="ts">
import type { BlogType } from '~/types/blog'
import { PlusCircle } from 'lucide-vue-next'
import { getLocaleContent } from '~/config/locales'

definePageMeta({
  middleware: ['auth'],
})

// Blog type enum options
const blogTypeOptions: {
  [key in BlogType]: { label: string, color: string }
} = {
  company: { label: 'Company', color: 'blue' },
  help: { label: 'Help', color: 'green' },
  terms: { label: 'Terms', color: 'amber' },
}

// Define the BlogListItem type
interface BlogListItem {
  id: number
  title: string | LocaleContent // Support both string and LocaleContent for backward compatibility
  slug: string
  type: string
  content: string
  status: boolean
  created_at: string
}

const tableSchema = [
  {
    key: 'title',
    label: 'Title',
  },
  {
    key: 'slug',
    label: 'Slug',
  },
  {
    key: 'status',
    label: 'Status',
  },
  {
    key: 'type',
    label: 'Type',
  },
  {
    key: 'created_at',
    label: 'Created At',
  },
]

const { getDefaultHeaders, clientFetch } = useCustomFetch()
const { notifySuccess } = useToast()
const route = useRoute()

// Pagination params
const routeQuery = computed(() => route.query)

const { data: pagination, refresh } = await useAsyncData('blogs', async () => {
  const { data } = await $fetch<ResponseData<Pagination<BlogListItem[]>>>('/api/blogs', {
    headers: getDefaultHeaders(),
    params: {
      page: routeQuery.value.page,
      per_page: routeQuery.value.per_page,
      q: routeQuery.value.q,
    },
  })

  return data
}, {
  watch: [routeQuery],
})

const blogs = computed(() => pagination.value?.data)

async function doDelete(item: BlogListItem) {
  const success = await clientFetch<ResponseData<boolean>>(`/api/blogs/${item.id}`, {
    method: 'DELETE',
  })

  if (success) {
    notifySuccess('Blog deleted successfully')
    await refresh()
  }
}

const isLoading = ref(false)
async function updateStatus(item: BlogListItem) {
  toggleStatus(item)
  isLoading.value = true
  const success = await clientFetch<ResponseData<boolean>>(`/api/blogs/${item.id}`, {
    method: 'PUT',
    body: {
      title: item.title,
      content: item.content,
      status: item.status,
      type: item.type,
    },
  })
  isLoading.value = false
  if (success) {
    notifySuccess('Blog status updated successfully')
  }
  else {
    toggleStatus(item)
  }
}

function toggleStatus(item: BlogListItem) {
  item.status = !item.status
}
</script>

<template>
  <div>
    <div class="flex justify-end my-2">
      <NuxtLink to="/blogs/new">
        <BaseButton size="xs" class="px-4">
          <PlusCircle class="w-3 h-3" />Create
        </BaseButton>
      </NuxtLink>
    </div>
    <BaseCard>
      <BaseCardHeader>
        <BaseCardTitle>Blogs</BaseCardTitle>
      </BaseCardHeader>
      <BaseCardContent>
        <TableTemplate :columns="tableSchema" :data="blogs">
          <template #title="{ item }">
            <NuxtLink :to="`/blogs/edit/${item.id}`">
              {{ getLocaleContent(item.title) }}
            </NuxtLink>
          </template>
          <template #slug="{ item }">
            {{ item.slug }}
          </template>
          <template #status="{ item }">
            <BaseSwitch :model-value="Boolean(item.status)" :disabled="isLoading" @update:model-value="updateStatus(item)" />
          </template>
          <template #type="{ item }">
            <BaseBadge variant="outline">
              {{ blogTypeOptions[item.type as BlogType]?.label || item.type }}
            </BaseBadge>
          </template>
          <template #created_at="{ item }">
            {{ new Date(item.created_at).toLocaleString() }}
          </template>
          <template #actions="{ item }">
            <TableButtonEdit :url="`/blogs/edit/${item.id}`" />
            <TableButtonDelete @delete="() => doDelete(item)" />
          </template>
        </TableTemplate>

        <!-- Pagination -->
        <div v-if="pagination && pagination.total > 0" class="mt-6">
          <TablePagination
            :total="pagination?.total || 0"
            :current="Number(routeQuery.page || 1)"
          />
        </div>
      </BaseCardContent>
    </BaseCard>
  </div>
</template>
