<script lang="ts" setup>
import { CheckSquare, ListPlus, Square, Trash } from 'lucide-vue-next'
import { onMounted, ref } from 'vue'

const props = defineProps<{
  collectionId?: string
}>()

const { clientFetch } = useCustomFetch()

// Use the composable for available products
const availableProductsListing = useProductQuickListing(
  `/api/collections/${props.collectionId}/products/search`,
)
const {
  loading: loadingAvailableProducts,
  searchQuery: availableSearchQuery,
  fetchProducts: fetchAvailableProducts,
  setSearchParams: setAvailableSearchParams,
} = availableProductsListing
const isBulkActionLoading = ref(false)

// Use the composable for selected products
const selectedProductsListing = useProductQuickListing(
  `/api/collections/${props.collectionId}/products`,
)
const {
  loading: loadingSelectedProducts,
  searchQuery: selectedSearchQuery,
  fetchProducts: fetchSelectedProducts,
  setSearchParams: setSelectedSearchParams,
} = selectedProductsListing

// Store the product lists
const availableProducts = ref<Pagination<ProductListForCollection[]> | null>(null)
const selectedProducts = ref<Pagination<ProductListForCollection[]> | null>(null)

// Track selected products for bulk actions
const selectedAvailableProductIds = ref<number[]>([])
const selectedCollectionProductIds = ref<number[]>([])

// Toggle product selection in available products list
function toggleAvailableProductSelection(productId: number) {
  const index = selectedAvailableProductIds.value.indexOf(productId)
  if (index !== -1)
    selectedAvailableProductIds.value.splice(index, 1)
  else
    selectedAvailableProductIds.value.push(productId)
}

// Toggle product selection in collection products list
function toggleCollectionProductSelection(productId: number) {
  const index = selectedCollectionProductIds.value.indexOf(productId)
  if (index !== -1)
    selectedCollectionProductIds.value.splice(index, 1)
  else
    selectedCollectionProductIds.value.push(productId)
}

// Select all products in a list
function selectAllAvailableProducts() {
  if (!availableProducts.value?.data)
    return

  if (selectedAvailableProductIds.value.length === availableProducts.value.data.length) {
    // If all are selected, deselect all
    selectedAvailableProductIds.value = []
  }
  else {
    // Select all
    selectedAvailableProductIds.value = availableProducts.value.data.map(p => p.id)
  }
}

function selectAllCollectionProducts() {
  if (!selectedProducts.value?.data)
    return

  if (selectedCollectionProductIds.value.length === selectedProducts.value.data.length) {
    // If all are selected, deselect all
    selectedCollectionProductIds.value = []
  }
  else {
    // Select all
    selectedCollectionProductIds.value = selectedProducts.value.data.map(p => p.id)
  }
}

// Bulk add selected products to collection
async function bulkAddProducts() {
  if (selectedAvailableProductIds.value.length === 0)
    return

  isBulkActionLoading.value = true
  const success = await clientFetch(`/api/collections/${props.collectionId}/products`, {
    method: 'POST',
    body: {
      product_ids: selectedAvailableProductIds.value,
    },
  })

  if (success) {
    useToast().notifySuccess(`Added ${selectedAvailableProductIds.value.length} products to collection`)
    selectedAvailableProductIds.value = []
    refreshProductLists()
  }
  else {
    useToast().notifyError('Failed to add products to collection')
  }
  isBulkActionLoading.value = false
}

// Bulk remove selected products from collection
async function bulkRemoveProducts() {
  if (selectedCollectionProductIds.value.length === 0)
    return

  isBulkActionLoading.value = true
  const success = await clientFetch(`/api/collections/${props.collectionId}/products`, {
    method: 'DELETE',
    body: {
      product_ids: selectedCollectionProductIds.value,
    },
  })

  if (success) {
    useToast().notifySuccess(`Removed ${selectedCollectionProductIds.value.length} products from collection`)
    selectedCollectionProductIds.value = []
    refreshProductLists()
  }
  else {
    useToast().notifyError('Failed to remove products from collection')
  }
  isBulkActionLoading.value = false
}

// Handle search for available products
function handleAvailableSearch(query: string) {
  setAvailableSearchParams({
    q: query,
    page: 1,
  })
  refreshAvailableProducts()
}

// Handle search for selected products
function handleSelectedSearch(query: string) {
  setSelectedSearchParams({
    q: query,
    page: 1,
  })
  refreshSelectedProducts()
}

// Handle pagination for available products
function changeAvailablePage(page: number) {
  setAvailableSearchParams({ page })
  refreshAvailableProducts()
}

// Handle pagination for selected products
function changeSelectedPage(page: number) {
  setSelectedSearchParams({ page })
  refreshSelectedProducts()
}

// Refresh available products
async function refreshAvailableProducts() {
  availableProducts.value = await fetchAvailableProducts()
}

// Refresh selected products
async function refreshSelectedProducts() {
  selectedProducts.value = await fetchSelectedProducts()
}

// Refresh both product lists
async function refreshProductLists() {
  await Promise.all([
    refreshAvailableProducts(),
    refreshSelectedProducts(),
  ])
}

// Initial load
onMounted(() => {
  refreshProductLists()
})
</script>

<template>
  <div class="grid grid-cols-2 gap-6">
    <!-- Collection Products Section -->
    <div class="border rounded-lg overflow-hidden">
      <div class="bg-gray-50 px-6 py-4 border-b">
        <div class="flex justify-between items-center">
          <div class="flex items-center space-x-4">
            <h3 class="text-lg font-medium">
              Products in Collection
            </h3>
            <label class="flex space-x-2 text-sm cursor-pointer">
              <input
                type="checkbox"
                :checked="selectedCollectionProductIds.length === (selectedProducts?.data?.length || 0)"
                :indeterminate="selectedCollectionProductIds.length > 0 && selectedCollectionProductIds.length < (selectedProducts?.data?.length || 0)"
                class="checkbox"
                @change="selectAllCollectionProducts"
              >
              <span class="text-gray-600 mt-0.5">
                Select All
              </span>
            </label>
          </div>

          <div v-if="selectedCollectionProductIds.length > 0" class="flex items-center space-x-3">
            <BaseButton
              size="sm"
              variant="success"
              :disabled="isBulkActionLoading"
              :class="{ loading: isBulkActionLoading }"
              @click="bulkRemoveProducts"
            >
              Remove ({{ selectedCollectionProductIds.length }})
            </BaseButton>
          </div>
        </div>
      </div>

      <div class="p-6">
        <CollectionsProductList
          :pagination="selectedProducts"
          :search-query="selectedSearchQuery"
          :is-loading="loadingSelectedProducts"
          :selected-product-ids="selectedCollectionProductIds"
          @update:search-query="handleSelectedSearch"
          @update:page="changeSelectedPage"
          @product-click="(productId: number) => toggleCollectionProductSelection(productId)"
        />
      </div>
    </div>

    <!-- Available Products Section -->
    <div class="border rounded-lg overflow-hidden">
      <div class="bg-gray-50 px-6 py-4 border-b">
        <div class="flex justify-between items-center">
          <div class="flex items-center space-x-4">
            <h3 class="text-lg font-medium">
              Available Products
            </h3>
            <label class="flex space-x-2 text-sm cursor-pointer">
              <input
                type="checkbox"
                :checked="selectedAvailableProductIds.length === (availableProducts?.data?.length || 0)"
                :indeterminate="selectedAvailableProductIds.length > 0 && selectedAvailableProductIds.length < (availableProducts?.data?.length || 0)"
                class="checkbox"
                @change="selectAllAvailableProducts"
              >
              <span class="text-gray-600 mt-0.5">
                Select All
              </span>
            </label>
          </div>

          <div v-if="selectedAvailableProductIds.length > 0" class="flex items-center space-x-3">
            <BaseButton
              size="sm"
              variant="success"
              :disabled="isBulkActionLoading"
              :class="{ loading: isBulkActionLoading }"
              @click="bulkAddProducts"
            >
              Add ({{ selectedAvailableProductIds.length }})
            </BaseButton>
          </div>
        </div>
      </div>

      <div class="p-6">
        <CollectionsProductList
          :pagination="availableProducts"
          :search-query="availableSearchQuery"
          :is-loading="loadingAvailableProducts"
          :selected-product-ids="selectedAvailableProductIds"
          @update:search-query="handleAvailableSearch"
          @update:page="changeAvailablePage"
          @product-click="(productId: number) => toggleAvailableProductSelection(productId)"
        />
      </div>
    </div>
  </div>
</template>
