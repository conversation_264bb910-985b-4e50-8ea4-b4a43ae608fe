<script lang="ts" setup>
import type { ProductListForSaleEvent } from '~/types/event'
import { ListPlus, Package, Search, Trash } from 'lucide-vue-next'
import { onMounted, ref } from 'vue'

const props = defineProps<{
  eventId?: string
}>()

const emit = defineEmits<{
  (e: 'productsUpdated'): void
}>()

const { clientFetch, getDefaultHeaders } = useCustomFetch()
const { notifySuccess, notifyError } = useToast()
const { withConfirm } = useConfirm()

// Loading states
const loadingAvailableProducts = ref(false)
const loadingEventProducts = ref(false)
const isBulkActionLoading = ref(false)

// Search queries
const availableSearchQuery = ref('')
const eventSearchQuery = ref('')

// Pagination states
const availableProductsPage = ref(1)
const eventProductsPage = ref(1)

// Store the product lists
const availableProducts = ref<Pagination<ProductListForSaleEvent[]> | null>(null)
const eventProducts = ref<Pagination<ProductListForSaleEvent[]> | null>(null)

// Track selected products for bulk actions
const selectedAvailableProductIds = ref<number[]>([])
const selectedEventProductIds = ref<number[]>([])

// Toggle product selection in available products list
function toggleAvailableProductSelection(productId: number) {
  const index = selectedAvailableProductIds.value.indexOf(productId)
  if (index !== -1)
    selectedAvailableProductIds.value.splice(index, 1)
  else
    selectedAvailableProductIds.value.push(productId)
}

// Toggle product selection in event products list
function toggleEventProductSelection(productId: number) {
  const index = selectedEventProductIds.value.indexOf(productId)
  if (index !== -1)
    selectedEventProductIds.value.splice(index, 1)
  else
    selectedEventProductIds.value.push(productId)
}

// Select all products in a list
function selectAllAvailableProducts() {
  if (!availableProducts.value?.data)
    return

  if (selectedAvailableProductIds.value.length === availableProducts.value.data.length) {
    selectedAvailableProductIds.value = []
  }
  else {
    selectedAvailableProductIds.value = availableProducts.value.data.map(p => p.id)
  }
}

function selectAllEventProducts() {
  if (!eventProducts.value?.data)
    return

  if (selectedEventProductIds.value.length === eventProducts.value.data.length) {
    selectedEventProductIds.value = []
  }
  else {
    selectedEventProductIds.value = eventProducts.value.data.map(p => p.id)
  }
}

// Bulk attach products to event
async function bulkAttachProducts() {
  if (selectedAvailableProductIds.value.length === 0)
    return

  isBulkActionLoading.value = true
  try {
    const result = await clientFetch<{ updated_count: number, message: string }>(`/api/sale-events/${props.eventId}/attach-products`, {
      method: 'POST',
      body: {
        product_ids: selectedAvailableProductIds.value,
      },
    })

    if (result) {
      notifySuccess(result.message)
      selectedAvailableProductIds.value = []
      await refreshProductLists()
      emit('productsUpdated')
    }
  }
  catch (error) {
    console.error('Error attaching products:', error)
    notifyError('Failed to add products to sale event')
  }
  finally {
    isBulkActionLoading.value = false
  }
}

// Bulk detach products from event
async function bulkDetachProducts() {
  if (selectedEventProductIds.value.length === 0)
    return

  isBulkActionLoading.value = true
  try {
    const result = await clientFetch<{ updated_count: number, message: string }>(`/api/sale-events/${props.eventId}/detach-products`, {
      method: 'POST',
      body: {
        product_ids: selectedEventProductIds.value,
      },
    })

    if (result) {
      notifySuccess(result.message)
      selectedEventProductIds.value = []
      await refreshProductLists()
      emit('productsUpdated')
    }
  }
  catch (error) {
    console.error('Error detaching products:', error)
    notifyError('Failed to remove products from sale event')
  }
  finally {
    isBulkActionLoading.value = false
  }
}

// Detach all products from event
async function detachAllProducts() {
  if (!eventProducts.value?.total || eventProducts.value.total === 0)
    return

  withConfirm({
    title: 'Remove All Products',
    description: `Are you sure you want to remove all ${eventProducts.value.total} products from this sale event?`,
    onConfirm: async () => {
      await performDetachAllProducts()
    },
  })
}

async function performDetachAllProducts() {
  isBulkActionLoading.value = true
  try {
    const result = await clientFetch<{ updated_count: number, message: string }>(`/api/sale-events/${props.eventId}/detach-products`, {
      method: 'POST',
      body: {}, // Empty body means detach all
    })

    if (result) {
      notifySuccess(result.message)
      selectedEventProductIds.value = []
      await refreshProductLists()
      emit('productsUpdated')
    }
  }
  catch (error) {
    console.error('Error detaching all products:', error)
    notifyError('Failed to remove all products from sale event')
  }
  finally {
    isBulkActionLoading.value = false
  }
}

// Fetch available products
async function fetchAvailableProducts() {
  loadingAvailableProducts.value = true
  try {
    const { data } = await $fetch<ResponseData<Pagination<ProductListForSaleEvent[]>>>('/api/sale-events/available-products/list', {
      headers: getDefaultHeaders(),
      params: {
        page: availableProductsPage.value,
        per_page: 20,
        search: availableSearchQuery.value,
      },
    })
    availableProducts.value = data
  }
  catch (error) {
    console.error('Error fetching available products:', error)
    notifyError('Failed to fetch available products')
  }
  finally {
    loadingAvailableProducts.value = false
  }
}

// Fetch event products
async function fetchEventProducts() {
  loadingEventProducts.value = true
  try {
    const { data } = await $fetch<ResponseData<Pagination<ProductListForSaleEvent[]>>>(`/api/sale-events/${props.eventId}/products`, {
      headers: getDefaultHeaders(),
      params: {
        page: eventProductsPage.value,
        per_page: 20,
        search: eventSearchQuery.value,
      },
    })
    eventProducts.value = data
  }
  catch (error) {
    console.error('Error fetching event products:', error)
    notifyError('Failed to fetch event products')
  }
  finally {
    loadingEventProducts.value = false
  }
}

// Refresh both product lists
async function refreshProductLists() {
  await Promise.all([
    fetchAvailableProducts(),
    fetchEventProducts(),
  ])
}

// Search handlers with debouncing
const debouncedAvailableSearch = debounce(() => {
  availableProductsPage.value = 1
  fetchAvailableProducts()
}, 500)

const debouncedEventSearch = debounce(() => {
  eventProductsPage.value = 1
  fetchEventProducts()
}, 500)

watch(availableSearchQuery, debouncedAvailableSearch)
watch(eventSearchQuery, debouncedEventSearch)

// Pagination handlers
function changeAvailablePage(page: number) {
  availableProductsPage.value = page
  fetchAvailableProducts()
}

function changeEventPage(page: number) {
  eventProductsPage.value = page
  fetchEventProducts()
}

// Initial load
onMounted(() => {
  refreshProductLists()
})
</script>

<template>
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Event Products Section -->
    <div class="border rounded-lg overflow-hidden bg-card">
      <div class="bg-muted/50 px-6 py-4 border-b">
        <div class="flex justify-between items-center">
          <div class="flex items-center space-x-4">
            <h3 class="text-lg font-semibold">
              Products in Sale Event
            </h3>
            <label class="flex items-center space-x-2 text-sm cursor-pointer">
              <input
                type="checkbox"
                :checked="selectedEventProductIds.length === (eventProducts?.data?.length || 0)"
                :indeterminate="selectedEventProductIds.length > 0 && selectedEventProductIds.length < (eventProducts?.data?.length || 0)"
                class="checkbox checkbox-sm"
                @change="selectAllEventProducts"
              >
              <span class="text-muted-foreground">
                Select All
              </span>
            </label>
          </div>

          <div class="flex items-center space-x-2">
            <BaseButton
              v-if="selectedEventProductIds.length > 0"
              size="sm"
              variant="destructive"
              :disabled="isBulkActionLoading"
              @click="bulkDetachProducts"
            >
              <Trash class="h-4 w-4 mr-2" />
              Remove ({{ selectedEventProductIds.length }})
            </BaseButton>
            <BaseButton
              v-else-if="eventProducts?.total && eventProducts.total > 0"
              size="sm"
              variant="outline"
              :disabled="isBulkActionLoading"
              @click="detachAllProducts"
            >
              Remove All
            </BaseButton>
          </div>
        </div>
      </div>

      <div class="p-6">
        <EventsProductList
          :pagination="eventProducts"
          :search-query="eventSearchQuery"
          :is-loading="loadingEventProducts"
          :selected-product-ids="selectedEventProductIds"
          @update:search-query="(q) => eventSearchQuery = q"
          @update:page="changeEventPage"
          @product-click="toggleEventProductSelection"
        />
      </div>
    </div>

    <!-- Available Products Section -->
    <div class="border rounded-lg overflow-hidden bg-card">
      <div class="bg-muted/50 px-6 py-4 border-b">
        <div class="flex justify-between items-center">
          <div class="flex items-center space-x-4">
            <h3 class="text-lg font-semibold">
              Available Products
            </h3>
            <label class="flex items-center space-x-2 text-sm cursor-pointer">
              <input
                type="checkbox"
                :checked="selectedAvailableProductIds.length === (availableProducts?.data?.length || 0)"
                :indeterminate="selectedAvailableProductIds.length > 0 && selectedAvailableProductIds.length < (availableProducts?.data?.length || 0)"
                class="checkbox checkbox-sm"
                @change="selectAllAvailableProducts"
              >
              <span class="text-muted-foreground">
                Select All
              </span>
            </label>
          </div>

          <div v-if="selectedAvailableProductIds.length > 0">
            <BaseButton
              size="sm"
              :disabled="isBulkActionLoading"
              @click="bulkAttachProducts"
            >
              <ListPlus class="h-4 w-4 mr-2" />
              Add ({{ selectedAvailableProductIds.length }})
            </BaseButton>
          </div>
        </div>
      </div>

      <div class="p-6">
        <EventsProductList
          :pagination="availableProducts"
          :search-query="availableSearchQuery"
          :is-loading="loadingAvailableProducts"
          :selected-product-ids="selectedAvailableProductIds"
          @update:search-query="(q) => availableSearchQuery = q"
          @update:page="changeAvailablePage"
          @product-click="toggleAvailableProductSelection"
        />
      </div>
    </div>
  </div>
</template>
