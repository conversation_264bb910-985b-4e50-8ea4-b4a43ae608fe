<script setup lang="ts">
import { Label } from '~/components/ui/label'
import { Switch } from '~/components/ui/switch'
import { TableCell, TableRow } from '~/components/ui/table'
import { DollarSign, Package } from 'lucide-vue-next'
import StockStatus from '~/components/Inventory/StockStatus.vue'

const { variant } = defineProps<{
  variant: Variant
}>()

const data = reactive({
  adjust_price: variant.adjust_price || 0,
  sapo_sku: variant.sapo_sku || '',
  status: variant.status || 'active',
  quantity: variant.quantity || 0,
})

const is_dirty = ref(false)
const requesting = ref(false)

watch(() => data.adjust_price, (n, o) => {
  const _n = n || 0
  const _o = o || 0

  if (_n !== _o) {
    data.adjust_price = _n
    is_dirty.value = true
  }
})

watch(() => data.sapo_sku, (n, o) => {
  const _n = n || ''
  const _o = o || ''

  if (_n !== _o) {
    data.sapo_sku = _n
    is_dirty.value = true
  }
})

watch(() => data.quantity, (n, o) => {
  const _n = n || 0
  const _o = o || 0

  if (_n !== _o) {
    data.quantity = _n
    is_dirty.value = true
  }
})

const status = computed({
  get: () => data.status === 'active',
  set: (value) => {
    data.status = value ? 'active' : 'inactive'
    is_dirty.value = true
    update()
  },
})

async function update() {
  try {
    requesting.value = true
    const { getDefaultHeaders } = useCustomFetch()
    const { notifySuccess } = useToast()

    await $fetch(`/api/v1/admin/variants/${variant.id}`, {
      method: 'PUT',
      headers: getDefaultHeaders(),
      body: data,
    })
    notifySuccess('Update successfully')
  }
  catch (error) {
    console.error(error)
  }
  finally {
    requesting.value = false
  }
}
</script>

<template>
  <TableRow>
    <TableCell class="font-medium">
      {{ variant.sku }}
    </TableCell>
    <TableCell>
        <div class="relative w-full max-w-sm items-center">
            <BaseInput
                v-model="data.adjust_price"
                :disabled="requesting"
                class="max-w-[100px] pl-10 text-right"
                @blur="is_dirty && update()"
            />
                <span class="absolute start-0 inset-y-0 flex items-center justify-center px-2">
          <DollarSign class="size-4 text-muted-foreground" />
            </span>
        </div>
    </TableCell>
    <TableCell class="text-right">
      <BaseInput
        v-model="data.sapo_sku"
        :disabled="requesting"
        @blur="is_dirty && update()"
      />
    </TableCell>
    <TableCell class="text-center">
      <div class="relative w-full max-w-sm items-center">
        <BaseInput
          v-model.number="data.quantity"
          :disabled="requesting"
          type="number"
          min="0"
          class="max-w-[100px] pl-10 text-right"
          @blur="is_dirty && update()"
        />
        <span class="absolute start-0 inset-y-0 flex items-center justify-center px-2">
          <Package class="size-4 text-muted-foreground" />
        </span>
      </div>
    </TableCell>
    <TableCell class="text-center">
      <StockStatus :quantity="data.quantity" />
    </TableCell>
    <TableCell class="text-right">
      <div class="flex items-center space-x-2">
        <Switch :id="`status-${variant.id}`" v-model="status" />
        <Label :for="`status-${variant.id}`" :class="{ 'cursor-not-allowed': requesting, 'text-gray-400': ! status }">
          {{ status ? 'Active' : 'Inactive' }}
        </Label>
      </div>
    </TableCell>
  </TableRow>
</template>
