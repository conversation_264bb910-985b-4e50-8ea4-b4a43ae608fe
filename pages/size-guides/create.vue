<script lang="ts" setup>
import { useSizeGuides } from '~/composables/custom/sizeGuides'

definePageMeta({
  middleware: ['auth'],
})

const router = useRouter()
const { clientFetch } = useCustomFetch()
const { notifySuccess } = useToast()
const { form, validate } = useSizeGuides()
const { hasChange } = useChange(form)

async function submit() {
  if (!validate()) {
    return
  }

  const success = await clientFetch('/api/size-guides', {
    method: 'POST',
    body: toRaw(form),
  })

  if (success) {
    notifySuccess('Size guide created successfully')
    router.push('/size-guides')
  }
}
</script>

<template>
  <div>
    <div class="flex items-center justify-between mt-2">
      <div class="text-lg uppercase">
        Create Size Guide
      </div>
      <BaseButton :disabled="!hasChange" @click="submit">
        Save Changes
      </BaseButton>
    </div>
    <BaseCard class="mt-4">
      <BaseCardHeader />
      <BaseCardContent>
        <div class="space-y-4">
          <FormInput
            id="size-guide-name"
            v-model="form.name"
            label="Name"
          />
          <SizeGuideForm
            v-model:table="form.table"
          />
        </div>
      </BaseCardContent>
    </BaseCard>
  </div>
</template>
