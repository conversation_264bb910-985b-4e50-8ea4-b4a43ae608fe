<script lang="ts" setup>
import { debounce } from '@/utils'
import { PlusCircle } from 'lucide-vue-next'

definePageMeta({
  middleware: ['auth'],
})

const tableSchema = [
  {
    key: 'thumbnail',
    label: 'Thumbnail',
  },
  {
    key: 'name',
    label: 'Name',
  },
  {
    key: 'sku',
    label: 'SKU',
  },
  {
    key: 'price',
    label: 'Price',
  },
  {
    key: 'status',
    label: 'Status',
  },
  {
    key: 'priority',
    label: 'Priority',
  },
]

const { getDefaultHeaders, clientFetch } = useCustomFetch()
const { notifySuccess } = useToast()
const route = useRoute()
const { setQueryParam } = useQueryParams()

// Pagination params
const routeQuery = computed(() => route.query)
const page = computed(() => Number(routeQuery.value.page || 1))
const perPage = computed(() => Number(routeQuery.value.per_page || 15))

// Search functionality - sync with route query
const search = ref(routeQuery.value.q as string || '')

// Fetch products with pagination
const { data: pagination, refresh } = await useAsyncData(
  'products',
  async () => {
    const { data } = await $fetch<ResponseData<Pagination<ProductListItem[]>>>('/api/products', {
      headers: getDefaultHeaders(),
      params: {
        page: page.value,
        per_page: perPage.value,
        q: routeQuery.value.q,
      },
    })

    return data
  },
  {
    watch: [routeQuery],
  },
)

const products = computed(() => pagination.value?.data || [])

async function doDelete(item: ProductListItem) {
  const success = await clientFetch<ResponseData<boolean>>(`/api/products/${item.id}`, {
    method: 'DELETE',
  })

  if (success) {
    notifySuccess('Product deleted successfully')
    refresh()
  }
}

const isLoading = ref(false)
async function updateStatus(item: ProductListItem) {
  toggleStatus(item)
  isLoading.value = true
  const success = await clientFetch<ResponseData<boolean>>(`/api/products/update-status/${item.id}`, {
    method: 'PUT',
    body: { status: item.status },
  })
  isLoading.value = false
  if (success) {
    notifySuccess('Product status updated successfully')
  }
  else {
    toggleStatus(item)
  }
}

function toggleStatus(item: ProductListItem) {
  item.status = item.status === 1 ? 0 : 1
}

async function updatePriority(item: ProductListItem, priority: number) {
  const success = await clientFetch<ResponseData<boolean>>(`/api/products/update-priority/${item.id}`, {
    method: 'PUT',
    body: { priority },
  })

  if (success) {
    notifySuccess('Product priority updated successfully')
    refresh()
  }
}

const updatePriorityDebounced = debounce(updatePriority, 1000)

// Debounced search implementation
const debouncedSearch = debounce(() => {
  setQueryParam({
    q: search.value,
    page: '1', // Reset to first page when searching
  })
}, 1000)

watch(() => search.value, () => {
  debouncedSearch()
})
</script>

<template>
  <div>
    <div class="flex justify-end my-2">
      <NuxtLink to="/products/create">
        <BaseButton size="xs" class="px-4">
          <PlusCircle class="w-3 h-3" />Create
        </BaseButton>
      </NuxtLink>
    </div>
    <BaseCard>
      <BaseCardHeader>
        <div class="flex items-center justify-between">
          <div class="text-xl font-medium">
            Products
          </div>
        </div>
      </BaseCardHeader>
      <BaseCardContent>
        <SearchOnly v-model="search" class="mb-4" placeholder="Search products by name" />
        <TableTemplate :columns="tableSchema" :data="products">
          <template #thumbnail="{ item }">
            <img :src="fileUrl(item.image.path)" class="w-16 h-24 object-cover">
          </template>
          <template #name="{ item }">
            <NuxtLink :to="`/products/edit/${item.id}`">
              {{ item.name }}
            </NuxtLink>
          </template>
          <template #sku="{ item }">
            {{ item.sku }}
          </template>
          <template #price="{ item }">
            {{ getPriceString(item) }}
          </template>
          <template #status="{ item }">
            <BaseSwitch :model-value="Boolean(item.status)" :disabled="isLoading" @update:model-value="updateStatus(item)" />
          </template>
          <template #actions="{ item }">
            <TableButtonEdit :url="`/products/edit/${item.id}`" />
            <TableButtonDelete @delete="() => doDelete(item)" />
          </template>
          <template #priority="{ item }">
            <BaseInput class="w-16" :model-value="item.priority" type="number" @update:model-value="updatePriorityDebounced(item, $event)" />
          </template>
        </TableTemplate>

        <!-- Pagination -->
        <div v-if="pagination && pagination.total > 0" class="mt-6">
          <TablePagination
            :total="pagination?.total || 0"
            :current="page"
          />
        </div>
      </BaseCardContent>
    </BaseCard>
  </div>
</template>
