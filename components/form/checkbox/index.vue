<script lang="ts" setup>
defineProps<{
  csId: string
  label: string
  modelValue: boolean
  type?: string
  placeholder?: string
  error?: string
}>()
defineEmits<{
  (e: 'update:modelValue', value: boolean): void
}>()
</script>

<template>
  <div class="flex items-center space-x-1">
    <BaseCheckbox
      :id="`checkbox-${csId}`"
      :checked="modelValue"
      @update:checked="$emit('update:modelValue', !modelValue)"
    />
    <BaseLabel
      :for="`checkbox-${csId}`"
      class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
    >
      {{ label }}
    </BaseLabel>
  </div>
</template>
