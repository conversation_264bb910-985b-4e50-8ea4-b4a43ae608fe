<script setup lang="ts">
import {
  Table,
  TableBody,
  TableCaption,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import Update from '~/components/Variants/Update.vue'

const { getDefaultHeaders } = useCustomFetch()
const route = useRoute()

// Fetch products with pagination
const { data: product } = await useAsyncData(
  'product',
  async () => {
    const { data } = await $fetch<ResponseData<ProductItem>>(`/api/products/${route.params.id}`, {
      headers: getDefaultHeaders(),
    })

    return data
  },
)
</script>

<template>
  <Table class="max-w-[800px]">
    <TableCaption>A list of your recent invoices.</TableCaption>
    <TableHeader>
      <TableRow>
        <TableHead class="w-[200px]">
          Sku
        </TableHead>
        <TableHead>Adjust Price</TableHead>
        <TableHead>SAPO Sku</TableHead>
        <TableHead>Status</TableHead>
      </TableRow>
    </TableHeader>
    <TableBody>
      <template v-for="(it, idx) in product?.variants" :key="idx">
        <Update :variant="it" />
      </template>
    </TableBody>
  </Table>
</template>
