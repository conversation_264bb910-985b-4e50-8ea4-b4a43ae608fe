export function useUpload() {
  const { notifySuccess, notifyError } = useToast()

  async function uploadTemp(files: FileList) {
    const config = useRuntimeConfig()
    const formData = new FormData()
    Array.from(files).forEach((file) => {
      formData.append('files[]', file)
    })

    const token = useCookie('token')

    try {
      notifySuccess('Đang tải ảnh lên...')
      const { data } = await $fetch('/files/upload-temp', {
        baseURL: config.public.baseURL, // khong goi qua proxy
        method: 'POST',
        body: formData,
        headers: {
          Authorization: `Bearer ${token.value}`,
        },
      }) as ResponseData<string[]>
      notifySuccess('Tải ảnh lên thành công')
      return data
    }
    catch (error) {
      notifyError('Có lỗi xảy ra khi tải ảnh lên')
      console.error(error)
      return null
    }
  }

  return {
    uploadTemp,
  }
}
