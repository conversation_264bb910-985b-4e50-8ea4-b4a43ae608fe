<script setup lang="ts">
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Button } from '~/components/ui/button'
import { Input } from '~/components/ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs'
import { Badge } from '~/components/ui/badge'
import InventorySummary from '~/components/Inventory/InventorySummary.vue'
import StockStatus from '~/components/Inventory/StockStatus.vue'
import StockManager from '~/components/Inventory/StockManager.vue'
import { Search, Package, AlertTriangle, TrendingDown, RefreshCw } from 'lucide-vue-next'

definePageMeta({
  layout: 'default'
})

const { getDefaultHeaders } = useCustomFetch()
const { notifyError } = useToast()

// Reactive data
const loading = ref(false)
const searchQuery = ref('')
const variants = ref<Variant[]>([])
const inventoryStats = ref({
  totalVariants: 0,
  inStockVariants: 0,
  lowStockVariants: 0,
  outOfStockVariants: 0,
  totalQuantity: 0
})

// Computed properties
const filteredVariants = computed(() => {
  if (!searchQuery.value) return variants.value
  
  const query = searchQuery.value.toLowerCase()
  return variants.value.filter(variant => 
    variant.sku.toLowerCase().includes(query) ||
    variant.sapo_sku?.toLowerCase().includes(query) ||
    variant.size.toLowerCase().includes(query)
  )
})

const lowStockVariants = computed(() => 
  variants.value.filter(variant => {
    const quantity = variant.quantity || 0
    return quantity > 0 && quantity <= 5
  })
)

const outOfStockVariants = computed(() => 
  variants.value.filter(variant => (variant.quantity || 0) === 0)
)

// Methods
async function fetchVariants() {
  try {
    loading.value = true
    
    const response = await $fetch('/api/v1/admin/variants', {
      headers: getDefaultHeaders()
    })
    
    variants.value = response.data || []
    calculateStats()
  } catch (error) {
    console.error('Failed to fetch variants:', error)
    notifyError('Failed to load inventory data')
  } finally {
    loading.value = false
  }
}

function calculateStats() {
  const stats = {
    totalVariants: variants.value.length,
    inStockVariants: 0,
    lowStockVariants: 0,
    outOfStockVariants: 0,
    totalQuantity: 0
  }

  variants.value.forEach(variant => {
    const quantity = variant.quantity || 0
    stats.totalQuantity += quantity
    
    if (quantity === 0) {
      stats.outOfStockVariants++
    } else if (quantity <= 5) {
      stats.lowStockVariants++
    } else {
      stats.inStockVariants++
    }
  })

  inventoryStats.value = stats
}

function onStockUpdated(updatedVariant: Variant) {
  const index = variants.value.findIndex(v => v.id === updatedVariant.id)
  if (index !== -1) {
    variants.value[index] = updatedVariant
    calculateStats()
  }
}

// Lifecycle
onMounted(() => {
  fetchVariants()
})
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">Inventory Management</h1>
        <p class="text-muted-foreground">
          Monitor and manage your product variant stock levels
        </p>
      </div>
      <Button @click="fetchVariants" :disabled="loading">
        <RefreshCw class="h-4 w-4 mr-2" :class="{ 'animate-spin': loading }" />
        Refresh
      </Button>
    </div>

    <!-- Inventory Summary -->
    <InventorySummary :stats="inventoryStats" />

    <!-- Main Content -->
    <Tabs default-value="all" class="space-y-4">
      <TabsList>
        <TabsTrigger value="all">
          <Package class="h-4 w-4 mr-2" />
          All Variants ({{ variants.length }})
        </TabsTrigger>
        <TabsTrigger value="low-stock">
          <AlertTriangle class="h-4 w-4 mr-2" />
          Low Stock ({{ lowStockVariants.length }})
        </TabsTrigger>
        <TabsTrigger value="out-of-stock">
          <TrendingDown class="h-4 w-4 mr-2" />
          Out of Stock ({{ outOfStockVariants.length }})
        </TabsTrigger>
      </TabsList>

      <!-- All Variants Tab -->
      <TabsContent value="all" class="space-y-4">
        <Card>
          <CardHeader>
            <div class="flex items-center justify-between">
              <div>
                <CardTitle>All Variants</CardTitle>
                <CardDescription>Complete inventory overview</CardDescription>
              </div>
              <div class="relative w-72">
                <Search class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  v-model="searchQuery"
                  placeholder="Search by SKU, size..."
                  class="pl-8"
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div v-if="loading" class="flex items-center justify-center py-8">
              <RefreshCw class="h-6 w-6 animate-spin mr-2" />
              Loading inventory...
            </div>
            
            <div v-else-if="filteredVariants.length === 0" class="text-center py-8 text-muted-foreground">
              No variants found
            </div>
            
            <Table v-else>
              <TableHeader>
                <TableRow>
                  <TableHead>SKU</TableHead>
                  <TableHead>Size</TableHead>
                  <TableHead>SAPO SKU</TableHead>
                  <TableHead class="text-center">Stock Quantity</TableHead>
                  <TableHead class="text-center">Status</TableHead>
                  <TableHead class="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-for="variant in filteredVariants" :key="variant.id">
                  <TableCell class="font-medium">{{ variant.sku }}</TableCell>
                  <TableCell>{{ variant.size }}</TableCell>
                  <TableCell>{{ variant.sapo_sku || '-' }}</TableCell>
                  <TableCell class="text-center font-mono">
                    {{ variant.quantity || 0 }}
                  </TableCell>
                  <TableCell class="text-center">
                    <StockStatus :quantity="variant.quantity || 0" />
                  </TableCell>
                  <TableCell class="text-right">
                    <StockManager :variant="variant" @stock-updated="onStockUpdated" />
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </TabsContent>

      <!-- Low Stock Tab -->
      <TabsContent value="low-stock" class="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <AlertTriangle class="h-5 w-5 text-yellow-600" />
              Low Stock Variants
            </CardTitle>
            <CardDescription>Variants with 5 or fewer units in stock</CardDescription>
          </CardHeader>
          <CardContent>
            <div v-if="lowStockVariants.length === 0" class="text-center py-8 text-muted-foreground">
              No low stock variants found
            </div>
            
            <Table v-else>
              <TableHeader>
                <TableRow>
                  <TableHead>SKU</TableHead>
                  <TableHead>Size</TableHead>
                  <TableHead class="text-center">Stock Quantity</TableHead>
                  <TableHead class="text-center">Status</TableHead>
                  <TableHead class="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-for="variant in lowStockVariants" :key="variant.id">
                  <TableCell class="font-medium">{{ variant.sku }}</TableCell>
                  <TableCell>{{ variant.size }}</TableCell>
                  <TableCell class="text-center font-mono text-yellow-600 font-semibold">
                    {{ variant.quantity || 0 }}
                  </TableCell>
                  <TableCell class="text-center">
                    <StockStatus :quantity="variant.quantity || 0" />
                  </TableCell>
                  <TableCell class="text-right">
                    <StockManager :variant="variant" @stock-updated="onStockUpdated" />
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </TabsContent>

      <!-- Out of Stock Tab -->
      <TabsContent value="out-of-stock" class="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center gap-2">
              <TrendingDown class="h-5 w-5 text-red-600" />
              Out of Stock Variants
            </CardTitle>
            <CardDescription>Variants that require immediate restocking</CardDescription>
          </CardHeader>
          <CardContent>
            <div v-if="outOfStockVariants.length === 0" class="text-center py-8 text-muted-foreground">
              No out of stock variants found
            </div>
            
            <Table v-else>
              <TableHeader>
                <TableRow>
                  <TableHead>SKU</TableHead>
                  <TableHead>Size</TableHead>
                  <TableHead class="text-center">Stock Quantity</TableHead>
                  <TableHead class="text-center">Status</TableHead>
                  <TableHead class="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-for="variant in outOfStockVariants" :key="variant.id">
                  <TableCell class="font-medium">{{ variant.sku }}</TableCell>
                  <TableCell>{{ variant.size }}</TableCell>
                  <TableCell class="text-center font-mono text-red-600 font-semibold">
                    0
                  </TableCell>
                  <TableCell class="text-center">
                    <StockStatus :quantity="0" />
                  </TableCell>
                  <TableCell class="text-right">
                    <StockManager :variant="variant" @stock-updated="onStockUpdated" />
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  </div>
</template>
