<script lang="ts" setup>
import { Loader } from 'lucide-vue-next'

interface ColorListItem {
  id: number
  name: string
  value: string
  product_count: number
}

interface Props {
  open: boolean
  color?: ColorListItem | null
}

interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'saved'): void
}

const props = withDefaults(defineProps<Props>(), {
  color: null,
})

const emit = defineEmits<Emits>()

const { clientFetch } = useCustomFetch()
const { notifySuccess, notifyError } = useToast()

// Computed properties
const isEditing = computed(() => props.color !== null)

// Form states
const isSaving = ref(false)

// Form data
const colorForm = ref({
  name: '',
  value: '#000000',
})

// Watch for prop changes to update form
watch(
  () => props.open,
  (newOpen) => {
    if (newOpen) {
      if (props.color) {
        // Edit mode - populate form with existing data
        colorForm.value = {
          name: props.color.name,
          value: props.color.value,
        }
      }
      else {
        // Create mode - reset to defaults
        colorForm.value = {
          name: 'New Color',
          value: '#000000',
        }
      }
    }
  },
  { immediate: true },
)

// Methods
function closeDialog() {
  emit('update:open', false)
  // Reset form after a short delay to avoid visual glitch
  setTimeout(() => {
    colorForm.value = {
      name: '',
      value: '#000000',
    }
    isSaving.value = false
  }, 150)
}

// Validate hex color input
function validateHexColor(value: string) {
  const hexRegex = /^#(?:[A-F0-9]{6}|[A-F0-9]{3})$/i
  return hexRegex.test(value)
}

// Handle color input change
function handleColorInput(event: Event) {
  const target = event.target as HTMLInputElement
  const value = target.value
  colorForm.value.value = value
}

// Save color (create or update)
async function saveColor() {
  if (!colorForm.value.name.trim() || colorForm.value.name.trim().length < 2) {
    notifyError('Color name must be at least 2 characters')
    return
  }

  if (!validateHexColor(colorForm.value.value)) {
    notifyError('Please enter a valid hex color code')
    return
  }

  isSaving.value = true

  const payload = {
    name: colorForm.value.name.trim(),
    value: colorForm.value.value.toUpperCase(),
  }

  try {
    if (isEditing.value && props.color) {
      // Update existing color
      const success = await clientFetch(`/api/colors/${props.color.id}`, {
        method: 'PUT',
        body: payload,
      })

      if (success) {
        notifySuccess('Color updated successfully')
        emit('saved')
        closeDialog()
      }
    }
    else {
      // Create new color
      const success = await clientFetch<ResponseData<ColorListItem>>('/api/colors', {
        method: 'POST',
        body: payload,
      })

      if (success) {
        notifySuccess('Color created successfully')
        emit('saved')
        closeDialog()
      }
    }
  }
  catch (error) {
    console.error('Failed to save color:', error)
    notifyError('Failed to save color')
  }
  finally {
    isSaving.value = false
  }
}
</script>

<template>
  <BaseDialog :open="open" @update:open="emit('update:open', $event)">
    <BaseDialogContent class="sm:max-w-md">
      <BaseDialogHeader>
        <BaseDialogTitle>
          {{ isEditing ? 'Edit Color' : 'Create New Color' }}
        </BaseDialogTitle>
        <BaseDialogDescription>
          {{ isEditing ? 'Update the color details below.' : 'Add a new color to your collection.' }}
        </BaseDialogDescription>
      </BaseDialogHeader>

      <div class="grid gap-6 py-4">
        <!-- Color Preview -->
        <div>
          <label class="block text-sm font-medium mb-2">
            Preview
          </label>
          <div class="flex items-center gap-4">
            <div
              class="w-16 h-16 rounded-lg border-2 border-gray-200 shadow-inner"
              :style="{ backgroundColor: colorForm.value }"
            />
            <div class="text-sm text-gray-600">
              <div class="font-medium">
                {{ colorForm.name || 'Color Name' }}
              </div>
              <div class="font-mono">
                {{ colorForm.value }}
              </div>
            </div>
          </div>
        </div>

        <!-- Name -->
        <div>
          <label for="color-name" class="block text-sm font-medium mb-2">
            Name <span class="text-red-500">*</span>
          </label>
          <BaseInput
            id="color-name"
            v-model="colorForm.name"
            placeholder="Enter color name"
            :disabled="isSaving"
          />
          <p class="text-xs text-gray-500 mt-1">
            e.g., "Ocean Blue", "Forest Green", "Sunset Orange"
          </p>
        </div>

        <!-- Color Value -->
        <div>
          <label for="color-value" class="block text-sm font-medium mb-2">
            Hex Color <span class="text-red-500">*</span>
          </label>
          <div class="flex items-center gap-2">
            <BaseInput
              id="color-value"
              v-model="colorForm.value"
              placeholder="#000000"
              class="flex-1 font-mono"
              :disabled="isSaving"
              @input="handleColorInput"
            />
            <!-- Native color picker -->
            <input
              type="color"
              :value="colorForm.value"
              class="w-10 h-10 border border-gray-300 rounded cursor-pointer"
              :disabled="isSaving"
              @input="handleColorInput"
            >
          </div>
          <p class="text-xs text-gray-500 mt-1">
            Enter a valid hex color code (e.g., #FF5733, #000)
          </p>
        </div>

        <!-- Product Count (Edit mode only) -->
        <div v-if="isEditing && color">
          <label class="block text-sm font-medium mb-2">
            Products Using This Color
          </label>
          <div class="text-sm text-gray-600">
            {{ color.product_count || 0 }} product(s)
          </div>
        </div>
      </div>

      <BaseDialogFooter>
        <BaseButton
          variant="outline"
          :disabled="isSaving"
          @click="closeDialog"
        >
          Cancel
        </BaseButton>
        <BaseButton
          :disabled="isSaving"
          class="flex items-center gap-2"
          @click="saveColor"
        >
          <Loader v-if="isSaving" class="h-4 w-4 animate-spin" />
          {{ isEditing ? 'Update Color' : 'Create Color' }}
        </BaseButton>
      </BaseDialogFooter>
    </BaseDialogContent>
  </BaseDialog>
</template>
