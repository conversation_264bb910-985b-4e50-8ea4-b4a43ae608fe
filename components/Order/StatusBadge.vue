<script setup lang="ts">
import {
  AlertCircle,
  CheckCircle,
  Clock,
  FileText,
  Package,
  Truck,
} from 'lucide-vue-next'

defineProps<{
  status: string
}>()
</script>

<template>
  <div>
    <!-- Status badge -->
    <BaseBadge
      v-if="status === 'draft'"
      variant="outline"
      class="text-gray-600 border-gray-600 text-xs"
    >
      <FileText class="w-3 h-3 mr-1" />
      Draft
    </BaseBadge>
    <BaseBadge
      v-else-if="status === 'pending'"
      variant="outline"
      class="text-yellow-600 border-yellow-600 text-xs"
    >
      <Clock class="w-3 h-3 mr-1" />
      Pending
    </BaseBadge>
    <BaseBadge
      v-else-if="status === 'processing'"
      variant="outline"
      class="text-blue-600 border-blue-600 text-xs"
    >
      <Package class="w-3 h-3 mr-1" />
      Processing
    </BaseBadge>
    <BaseBadge
      v-else-if="status === 'shipped'"
      variant="outline"
      class="text-purple-600 border-purple-600 text-xs"
    >
      <Truck class="w-3 h-3 mr-1" />
      Shipped
    </BaseBadge>
    <BaseBadge
      v-else-if="status === 'completed'"
      variant="outline"
      class="text-green-600 border-green-600 text-xs"
    >
      <CheckCircle class="w-3 h-3 mr-1" />
      Completed
    </BaseBadge>
    <BaseBadge
      v-else-if="status === 'cancelled'"
      variant="outline"
      class="text-red-600 border-red-600 text-xs"
    >
      <AlertCircle class="w-3 h-3 mr-1" />
      Cancelled
    </BaseBadge>
    <BaseBadge v-else variant="outline" class="text-xs">
      {{ status }}
    </BaseBadge>
  </div>
</template>
