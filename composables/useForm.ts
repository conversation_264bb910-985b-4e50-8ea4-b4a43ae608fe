export function useForm<T extends FormSchema>(schema: T) {
  const { initFormData, initErrors } = init(schema)

  const form = reactive(initFormData)
  const errors = reactive(initErrors)

  setupAutoClearErrorsOnFieldChange(form, errors, schema)

  const validateField = (field: keyof T): boolean => {
    (errors as Record<keyof T, string>)[field] = ''
    const rules = schema[field].rules
    for (const rule of rules) {
      const result = rule((form as Record<keyof T, string>)[field])
      if (result !== true) {
        (errors as Record<keyof T, string>)[field] = result
        return false
      }
    }
    return true
  }

  const validate = (): boolean => {
    let isValid = true;
    (Object.keys(schema) as Array<keyof T>).forEach((field) => {
      if (!validateField(field)) {
        isValid = false
      }
    })
    return isValid
  }

  const setForm = (newData: Partial<{ [K in keyof T]: T[K]['default'] }>): void => {
    Object.keys(newData).forEach((key) => {
      const field = key as keyof T
      if (field in form) {
        form[field] = newData[field]
      }
    })
  }

  function reset() {
    Object.assign(form, initFormData)
    Object.assign(errors, initErrors)
  }

  return { form, errors, validateField, validate, setForm, reset }
}

/*
  Init form data and errors from schema
*/
function init<T extends FormSchema>(schema: T) {
  const initFormData = {} as { [K in keyof T]: T[K]['default'] }
  const initErrors = {} as { [K in keyof T]: string }

  (Object.keys(schema) as Array<keyof T>).forEach((field) => {
    initFormData[field] = schema[field].default
    initErrors[field] = ''
  })

  return { initFormData, initErrors }
}

function setupAutoClearErrorsOnFieldChange<T extends FormSchema>(
  form: { [K in keyof T]: T[K]['default'] },
  errors: { [K in keyof T]: string },
  schema: T,
) {
  (Object.keys(schema) as Array<keyof T>).forEach((field) => {
    watch(
      () => form[field],
      () => {
        errors[field] = ''
      },
    )
  })
}
