<script setup lang="ts">
import { debounce } from '@/utils'
import { Search } from 'lucide-vue-next'

interface Props {
  pagination: Pagination<ProductListForCollection[]> | null
  searchQuery: string
  isLoading: boolean
  selectedProductIds: number[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'update:searchQuery', query: string): void
  (e: 'update:page', page: number): void
  (e: 'productClick', productId: number): void
}>()

function onProductClick(productId: number) {
  emit('productClick', productId)
}

function changePage(page: number) {
  emit('update:page', page)
}

// Local search query for v-model
const localSearchQuery = ref('')

// Watch for changes in the prop and update local value
watch(() => props.searchQuery, (newValue) => {
  localSearchQuery.value = newValue
}, { immediate: true })

// Watch local search query and emit changes (debounced)
const debouncedEmitSearch = debounce((value: string) => {
  emit('update:searchQuery', value)
}, 1500)

watch(localSearchQuery, (newValue) => {
  debouncedEmitSearch(newValue)
})
</script>

<template>
  <div class="space-y-4">
    <!-- Search -->
    <div class="relative">
      <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
      <BaseInput
        v-model="localSearchQuery"
        placeholder="Search by name or SKU"
        class="pl-9 w-full"
      />
    </div>

    <!-- Products List -->
    <div class="border rounded-md bg-white">
      <div v-if="isLoading" class="p-8 text-center text-muted-foreground">
        <div class="flex items-center justify-center space-x-2">
          <div class="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          <span>Loading...</span>
        </div>
      </div>
      <div v-else-if="!pagination || pagination.data.length === 0" class="p-8 text-center text-muted-foreground">
        <div class="space-y-2">
          <div class="text-2xl">
            📦
          </div>
          <p>No products found</p>
          <p class="text-xs">
            Try adjusting your search terms
          </p>
        </div>
      </div>
      <div v-else class="divide-y max-h-[500px] overflow-y-auto">
        <div
          v-for="product in pagination.data"
          :key="product.id"
          class="p-4 cursor-pointer transition-all duration-200 hover:bg-blue-50 group relative"
          :class="{
            'bg-blue-100 border-l-4 border-l-blue-500 shadow-sm': selectedProductIds.includes(product.id),
            'hover:shadow-sm': !selectedProductIds.includes(product.id),
          }"
          @click="onProductClick(product.id)"
        >
          <div class="flex items-center space-x-4">
            <!-- Checkbox -->
            <div class="flex-shrink-0">
              <input
                type="checkbox"
                :checked="selectedProductIds.includes(product.id)"
                class="checkbox checkbox-primary"
                @click.stop
                @change="onProductClick(product.id)"
              >
            </div>

            <!-- Product Image -->
            <div class="flex-shrink-0 w-16 h-16 bg-gray-100 rounded-lg overflow-hidden">
              <img
                v-if="product.image?.path"
                :src="fileUrl(product.image.path)"
                :alt="product.name"
                class="w-full h-full object-cover"
              >
              <div v-else class="w-full h-full flex items-center justify-center text-gray-400 text-xs">
                No Image
              </div>
            </div>

            <!-- Product Info -->
            <div class="flex-1 min-w-0">
              <div class="flex items-start justify-between">
                <div class="flex-1 min-w-0">
                  <h4 class="font-medium text-gray-900 truncate group-hover:text-blue-600 transition-colors">
                    {{ product.name }}
                  </h4>
                  <p class="text-sm text-gray-500 mt-1">
                    SKU: <span class="font-mono">{{ product.sku }}</span>
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Click indicator overlay -->
          <div
            v-if="selectedProductIds.includes(product.id)"
            class="absolute inset-0 pointer-events-none"
            style="background: linear-gradient(90deg, rgba(59, 130, 246, 0.05) 0%, transparent 100%)"
          />
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div v-if="pagination?.total && pagination.total > 0" class="flex items-center justify-between pt-4">
      <div class="text-sm text-gray-600">
        Showing <span class="font-medium">{{ pagination.data.length }}</span> of
        <span class="font-medium">{{ pagination.total }}</span> products
      </div>
      <div class="flex items-center space-x-2">
        <BaseButton
          size="sm"
          variant="outline"
          :disabled="pagination.current_page === 1"
          @click="changePage(pagination.current_page - 1)"
        >
          Previous
        </BaseButton>
        <div class="px-3 py-1 text-sm text-gray-600">
          Page {{ pagination.current_page }} of {{ pagination.last_page }}
        </div>
        <BaseButton
          size="sm"
          variant="outline"
          :disabled="pagination.current_page === pagination.last_page"
          @click="changePage(pagination.current_page + 1)"
        >
          Next
        </BaseButton>
      </div>
    </div>
  </div>
</template>
