<script lang="ts" setup>
import type { SaleEventFormData } from '~/types/event'

definePageMeta({
  middleware: ['auth'],
})

const { clientFetch } = useCustomFetch()
const { notifySuccess, notifyError } = useToast()
const router = useRouter()

// Initialize form data
const form = ref<SaleEventFormData>({
  name: '',
  start_date: new Date().toISOString().split('T')[0], // Today
  end_date: '',
  sale_percent: 0,
  status: true,
  description: '',
})

// Validation state
const errors = ref<Record<string, string>>({})

// Loading state
const isSubmitting = ref(false)

// Form component reference
const formRef = ref<{ validateForm: () => boolean } | null>(null)

// Submit form
async function handleSubmit() {
  // Validate using the form component
  if (!formRef.value?.validateForm()) {
    notifyError('Please fix the errors in the form')
    return
  }

  // Additional create-specific validation
  if (form.value.start_date && form.value.end_date) {
    const start = new Date(form.value.start_date)
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    if (start < today) {
      errors.value.start_date = 'Start date cannot be in the past'
      notifyError('Start date cannot be in the past')
      return
    }
  }

  isSubmitting.value = true

  try {
    const success = await clientFetch('/api/sale-events', {
      method: 'POST',
      body: form.value,
    })

    if (success) {
      notifySuccess('Sale event created successfully')
      router.push(`/events`)
    }
    else {
      notifyError('Failed to create sale event')
    }
  }
  catch (error) {
    console.error('Error creating sale event:', error)
    notifyError('Failed to create sale event')
  }
  finally {
    isSubmitting.value = false
  }
}
</script>

<template>
  <div class="max-w-4xl">
    <div class="flex items-center justify-between mb-6">
      <div>
        <h1 class="text-2xl font-bold">
          Create Sale Event
        </h1>
        <p class="text-muted-foreground mt-1">
          Set up a new promotional sale event for your products
        </p>
      </div>
      <GoBack />
    </div>

    <EventsForm
      ref="formRef"
      v-model:form="form"
      :errors="errors"
      :is-submitting="isSubmitting"
      @submit="handleSubmit"
    >
      <template #actions="{ isSubmitting: formSubmitting }">
        <div class="flex items-center justify-end gap-4">
          <BaseButton
            type="button"
            variant="outline"
            @click="router.back()"
          >
            Cancel
          </BaseButton>
          <BaseButton
            type="submit"
            :disabled="formSubmitting"
          >
            <template v-if="formSubmitting">
              <div class="flex items-center gap-2">
                <div class="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                Creating...
              </div>
            </template>
            <template v-else>
              Create Sale Event
            </template>
          </BaseButton>
        </div>
      </template>
    </EventsForm>
  </div>
</template>
