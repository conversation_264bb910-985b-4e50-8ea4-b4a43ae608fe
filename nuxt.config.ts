// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2024-11-01',
  devtools: {
    enabled: true,
  },
  modules: [
    '@nuxtjs/tailwindcss',
    'shadcn-nuxt',
    '@nuxtjs/color-mode',
    '@pinia/nuxt',
    'nuxt-tiptap-editor',
  ],
  css: [
    '~/assets/css/main.css',
    '~/assets/css/editor.css',
  ],
  tiptap: {
    prefix: 'Tiptap',
  },
  shadcn: {
    prefix: 'base',
    componentDir: './components/ui',
  },
  nitro: {
    esbuild: {
      options: {
        target: 'esnext',
      },
    },
    prerender: {
      crawlLinks: false,
      routes: [],
    },
  },
  runtimeConfig: {
    public: {
      baseURL: process.env.API_BASE_URL || 'https://backend-studio.kongs-production.ru/api/admin',
      imageUrl: process.env.IMAGE_BASE_URL,
      tinymceApiKey: process.env.TINYMCE_API_KEY,
    },
  },
})
