import { debounce } from '@/utils'
import { slugify } from '@/utils/string'
import { useForm } from '~/composables/useForm'
import { minLength, required } from '~/utils/validationRules'

export default function (isFinished: Ref<boolean>) {
  const { clientFetch } = useCustomFetch()
  const id = useRoute().params.id as string | undefined

  const locations = computed(() => useSettingsStore().locations)
  const { form, errors, validate, setForm } = useForm({
    sku: {
      default: '',
      rules: [required],
    },
    slug: {
      default: '',
      rules: [required, minLength(5, 'Slug must be at least 5 characters')],
    },
    name: {
      default: '',
      rules: [required],
    },
    description: {
      default: '' as string | null,
      rules: [],
    },
    status: {
      default: true,
      rules: [],
    },
    images: {
      default: [] as FileForm[],
      rules: [minLength(2, 'Please upload at least two image')],
    },
    size_guide_id: {
      default: null as number | null,
      rules: [],
    },
    collections: {
      default: [] as string[],
      rules: [],
    },
    options: {
      default: {
        colors: [],
        sizes: ['XS', 'S', 'M', 'L', 'XL'],
      },
      rules: [],
    },
    locationPrices: {
      default: initBasePrice(),
      rules: [],
    },
    compare_price: {
      default: 0,
      rules: [],
    },
    price: {
      default: 0,
      rules: [required],
    },
  })

  const isSlugValid = ref(true)
  const isSkuValid = ref(true)
  const isCheckingSlug = ref(false)
  const isCheckingSku = ref(false)

  const checkSlug = debounce(async (slug: string) => {
    if (slug && slug.length > 4) {
      let endpoint = `/api/products/is-slug-unique/${slug}`
      if (id) {
        endpoint += `/${id}`
      }

      const result = await clientFetch(endpoint, {
        method: 'GET',
      })

      isSlugValid.value = Boolean(result)
    }
    isCheckingSlug.value = false
  }, 500)

  const checkSku = debounce(async (sku: string) => {
    let endpoint = `/api/products/is-sku-unique/${sku}`
    if (id) {
      endpoint += `/${id}`
    }

    const result = await clientFetch(endpoint, {
      method: 'GET',
    })

    isSkuValid.value = Boolean(result)
    isCheckingSku.value = false
  }, 500)
  watch(() => form.name, () => {
    form.slug = slugify(form.name)
  })

  watch(() => form.slug, (slug) => {
    if (!isFinished.value) {
      return
    }

    isCheckingSlug.value = true
    checkSlug(slug)
  })

  watch(() => form.sku, (sku) => {
    if (!isFinished.value) {
      return
    }

    isCheckingSku.value = true
    checkSku(sku)
  })

  function initBasePrice() {
    const records = [] as BasePriceForm[]
    locations.value.forEach((location) => {
      records.push({
        id: location.id,
        price: 0,
        compare_price: 0,
        country: location.name,
      })
    })
    return records
  }

  return {
    form,
    errors,
    validate,
    isSlugValid,
    isCheckingSlug,
    isSkuValid,
    isCheckingSku,
    setForm,
  }
}
