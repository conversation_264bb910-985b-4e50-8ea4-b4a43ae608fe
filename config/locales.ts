export interface LocaleConfig {
  code: string
  name: string
  displayName: string
}

export const SUPPORTED_LOCALES: LocaleConfig[] = [
  {
    code: 'en',
    name: 'English',
    displayName: 'English',
  },
  {
    code: 'vi',
    name: 'Vietnamese',
    displayName: 'Vietnamese',
  },
] as const

// Extract locale codes for type safety
export const LOCALE_CODES = SUPPORTED_LOCALES.map(locale => locale.code)

// Default locale (first one in the list)
export const DEFAULT_LOCALE = SUPPORTED_LOCALES[0].code

// Helper functions
export function getLocaleName(code: string): string {
  return SUPPORTED_LOCALES.find(locale => locale.code === code)?.name || code
}

export function getLocaleDisplayName(code: string): string {
  return SUPPORTED_LOCALES.find(locale => locale.code === code)?.displayName || code
}

export function isValidLocale(code: string): boolean {
  return SUPPORTED_LOCALES.some(locale => locale.code === code)
}

// Create empty locale content object
export function createEmptyLocaleContent(): Record<string, string> {
  const content: Record<string, string> = {}
  SUPPORTED_LOCALES.forEach((locale) => {
    content[locale.code] = ''
  })
  return content
}

// Get locale content with fallback
export function getLocaleContent(content: Record<string, string> | string, preferredLocale?: string): string {
  if (typeof content === 'string') {
    return content
  }

  const locale = preferredLocale || DEFAULT_LOCALE

  // Return preferred locale if available
  if (content[locale]?.trim()) {
    return content[locale]
  }

  // Fallback to default locale
  if (content[DEFAULT_LOCALE]?.trim()) {
    return content[DEFAULT_LOCALE]
  }

  // Fallback to any available content
  for (const localeConfig of SUPPORTED_LOCALES) {
    if (content[localeConfig.code]?.trim()) {
      return content[localeConfig.code]
    }
  }

  return 'Untitled'
}
