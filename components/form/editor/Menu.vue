<script lang="ts" setup>
import type { Editor } from '@tiptap/core'
import type { Level } from '@tiptap/extension-heading'
import { Bold, CodeXml, Heading, Italic, List, Redo, Strikethrough, Undo } from 'lucide-vue-next'

const props = defineProps<{
  editor: Editor
  isRawMode: boolean
}>()
defineEmits(['toggleRaw'])

const headingOptions = [
  { label: 'Heading 1', func: makeHeading(1) },
  { label: 'Heading 2', func: makeHeading(2) },
  { label: 'Heading 3', func: makeHeading(3) },
  { label: 'Heading 4', func: makeHeading(4) },
  { label: 'Heading 5', func: makeHeading(5) },
  { label: 'Heading 6', func: makeHeading(6) },
]

function makeHeading(level: Level) {
  return () => {
    props.editor?.chain().focus().toggleHeading({ level }).run()
  }
}
</script>

<template>
  <div class="bg-gray-50 rounded">
    <FormEditorButton
      tooltip-content="Bold"
      :is-disabled="!editor.can().chain().focus().toggleBold().run()"
      @on-content-click="editor.chain().focus().toggleBold().run()"
    >
      <Bold class="w-4 h-4" />
    </FormEditorButton>
    <FormEditorButton
      tooltip-content="Italic"
      :is-disabled="!editor.can().chain().focus().toggleItalic().run()"
      @on-content-click="editor.chain().focus().toggleItalic().run()"
    >
      <Italic class="w-4 h-4" />
    </FormEditorButton>
    <FormEditorButton
      tooltip-content="Strikethrough"
      :is-disabled="!editor.can().chain().focus().toggleStrike().run()"
      @on-content-click="editor.chain().focus().toggleStrike().run()"
    >
      <Strikethrough class="w-4 h-4" />
    </FormEditorButton>
    <FormEditorDropdown :options="headingOptions" tooltip-content="Heading">
      <Heading class="w-4 h-4" />
    </FormEditorDropdown>
    <FormEditorButton
      tooltip-content="List"
      :is-disabled="!editor.can().chain().focus().toggleBulletList().run()"
      @on-content-click="editor.chain().focus().toggleBulletList().run()"
    >
      <List class="w-4 h-4" />
    </FormEditorButton>
    <FormEditorButton
      tooltip-content="Undo"
      :is-disabled="!editor.can().chain().focus().undo().run()"
      @on-content-click="editor.chain().focus().undo().run()"
    >
      <Undo class="w-4 h-4" />
    </FormEditorButton>
    <FormEditorButton
      tooltip-content="Redo"
      :is-disabled="!editor.can().chain().focus().redo().run()"
      @on-content-click="editor.chain().focus().redo().run()"
    >
      <Redo class="w-4 h-4" />
    </FormEditorButton>
    <FormEditorButton
      tooltip-content="Toggle Raw HTML"
      @on-content-click="$emit('toggleRaw')"
    >
      <CodeXml class="w-4 h-4" :class="{ 'text-blue-500': isRawMode }" />
    </FormEditorButton>
  </div>
</template>
