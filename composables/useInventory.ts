export interface InventoryStats {
  totalVariants: number
  inStockVariants: number
  lowStockVariants: number
  outOfStockVariants: number
  totalQuantity: number
}

export interface StockUpdateRequest {
  quantity: number
}

export const useInventory = () => {
  const { getDefaultHeaders } = useCustomFetch()
  const { notifySuccess, notifyError } = useToast()

  /**
   * Fetch all variants with inventory data
   */
  const fetchVariants = async (): Promise<Variant[]> => {
    try {
      const response = await $fetch<ResponseData<Variant[]>>('/api/v1/admin/variants', {
        headers: getDefaultHeaders()
      })
      return response.data || []
    } catch (error) {
      console.error('Failed to fetch variants:', error)
      notifyError('Failed to load variants')
      return []
    }
  }

  /**
   * Fetch low stock variants
   */
  const fetchLowStockVariants = async (threshold: number = 5): Promise<Variant[]> => {
    try {
      const response = await $fetch<ResponseData<Variant[]>>(`/api/v1/admin/variants/low-stock?threshold=${threshold}`, {
        headers: getDefaultHeaders()
      })
      return response.data || []
    } catch (error) {
      console.error('Failed to fetch low stock variants:', error)
      notifyError('Failed to load low stock variants')
      return []
    }
  }

  /**
   * Fetch out of stock variants
   */
  const fetchOutOfStockVariants = async (): Promise<Variant[]> => {
    try {
      const response = await $fetch<ResponseData<Variant[]>>('/api/v1/admin/variants/out-of-stock', {
        headers: getDefaultHeaders()
      })
      return response.data || []
    } catch (error) {
      console.error('Failed to fetch out of stock variants:', error)
      notifyError('Failed to load out of stock variants')
      return []
    }
  }

  /**
   * Update stock quantity for a variant
   */
  const updateStock = async (variantId: number | string, quantity: number): Promise<Variant | null> => {
    try {
      const response = await $fetch<ResponseData<Variant>>(`/api/v1/admin/variants/${variantId}/stock`, {
        method: 'PUT',
        headers: getDefaultHeaders(),
        body: { quantity }
      })
      notifySuccess('Stock updated successfully')
      return response.data
    } catch (error: any) {
      console.error('Failed to update stock:', error)
      const message = error?.data?.message || 'Failed to update stock'
      notifyError(message)
      return null
    }
  }

  /**
   * Increase stock quantity for a variant
   */
  const increaseStock = async (variantId: number | string, quantity: number): Promise<Variant | null> => {
    try {
      const response = await $fetch<ResponseData<Variant>>(`/api/v1/admin/variants/${variantId}/stock/increase`, {
        method: 'POST',
        headers: getDefaultHeaders(),
        body: { quantity }
      })
      notifySuccess(`Stock increased by ${quantity} units`)
      return response.data
    } catch (error: any) {
      console.error('Failed to increase stock:', error)
      const message = error?.data?.message || 'Failed to increase stock'
      notifyError(message)
      return null
    }
  }

  /**
   * Decrease stock quantity for a variant
   */
  const decreaseStock = async (variantId: number | string, quantity: number): Promise<Variant | null> => {
    try {
      const response = await $fetch<ResponseData<Variant>>(`/api/v1/admin/variants/${variantId}/stock/decrease`, {
        method: 'POST',
        headers: getDefaultHeaders(),
        body: { quantity }
      })
      notifySuccess(`Stock decreased by ${quantity} units`)
      return response.data
    } catch (error: any) {
      console.error('Failed to decrease stock:', error)
      const message = error?.data?.message || 'Failed to decrease stock'
      notifyError(message)
      return null
    }
  }

  /**
   * Calculate inventory statistics from variants array
   */
  const calculateInventoryStats = (variants: Variant[], lowStockThreshold: number = 5): InventoryStats => {
    const stats: InventoryStats = {
      totalVariants: variants.length,
      inStockVariants: 0,
      lowStockVariants: 0,
      outOfStockVariants: 0,
      totalQuantity: 0
    }

    variants.forEach(variant => {
      const quantity = variant.quantity || 0
      stats.totalQuantity += quantity

      if (quantity === 0) {
        stats.outOfStockVariants++
      } else if (quantity <= lowStockThreshold) {
        stats.lowStockVariants++
      } else {
        stats.inStockVariants++
      }
    })

    return stats
  }

  /**
   * Check if a variant is in stock
   */
  const isInStock = (variant: Variant): boolean => {
    return (variant.quantity || 0) > 0
  }

  /**
   * Check if a variant is low stock
   */
  const isLowStock = (variant: Variant, threshold: number = 5): boolean => {
    const quantity = variant.quantity || 0
    return quantity > 0 && quantity <= threshold
  }

  /**
   * Check if a variant is out of stock
   */
  const isOutOfStock = (variant: Variant): boolean => {
    return (variant.quantity || 0) === 0
  }

  /**
   * Get stock status for a variant
   */
  const getStockStatus = (variant: Variant, lowStockThreshold: number = 5) => {
    const quantity = variant.quantity || 0
    
    if (quantity === 0) {
      return {
        status: 'out-of-stock' as const,
        label: 'Out of Stock',
        color: 'red',
        priority: 'critical' as const
      }
    } else if (quantity <= lowStockThreshold) {
      return {
        status: 'low-stock' as const,
        label: 'Low Stock',
        color: 'yellow',
        priority: 'warning' as const
      }
    } else {
      return {
        status: 'in-stock' as const,
        label: 'In Stock',
        color: 'green',
        priority: 'normal' as const
      }
    }
  }

  return {
    // API methods
    fetchVariants,
    fetchLowStockVariants,
    fetchOutOfStockVariants,
    updateStock,
    increaseStock,
    decreaseStock,
    
    // Utility methods
    calculateInventoryStats,
    isInStock,
    isLowStock,
    isOutOfStock,
    getStockStatus
  }
}
