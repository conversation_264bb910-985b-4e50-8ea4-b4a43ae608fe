<script setup lang="ts">
import { debounce } from '@/utils'
import { Loader, Plus, Search } from 'lucide-vue-next'
import { createEmptyLocaleContent, DEFAULT_LOCALE, getLocaleContent } from '~/config/locales'

const props = defineProps<{
  exclude?: Collection[]
}>()
const emit = defineEmits(['create'])
const searchQuery = defineModel<string>('searchQuery', { required: true })

const { clientFetch } = useCustomFetch()
const collectionStore = useCollectionStore()

const isCreating = ref(false)
const isChecking = ref(false)
const collectionExists = ref(false)

const debouncedCheckExists = debounce(async (name: string) => {
  if (!name.trim()) {
    collectionExists.value = false
    return
  }

  isChecking.value = true
  const exists = await clientFetch<boolean>('/api/collections/check-exists', {
    method: 'GET',
    params: {
      name: name.trim(),
    },
  })

  collectionExists.value = Boolean(exists)
  isChecking.value = false
}, 1000)

watch(() => searchQuery.value, (newValue) => {
  if (isChecking.value)
    return

  if (newValue && newValue.trim()) {
    isChecking.value = true
    debouncedCheckExists(newValue)
  }
  else {
    collectionExists.value = false
  }
})

async function handleCreateNewCollection() {
  if (isCreating.value || collectionExists.value)
    return

  isCreating.value = true
  try {
    // Create LocaleContent object for the new collection name
    const nameContent = createEmptyLocaleContent()
    nameContent[DEFAULT_LOCALE] = searchQuery.value.trim()

    const collection = await clientFetch<Collection>('/api/collections', {
      method: 'POST',
      body: {
        name: nameContent,
      },
    })

    if (collection) {
      useToast().notifySuccess('Collection created successfully')
      collectionStore.fetch(true)
      emit('create', collection)
      searchQuery.value = ''
    }
  }
  catch (error) {
    console.error('Error creating collection:', error)
    useToast().notifyError('Failed to create collection')
  }
  finally {
    isCreating.value = false
  }
}

const showCreateButton = computed(() => {
  const query = searchQuery.value?.toLowerCase()
  return query
    && !isChecking.value
    && !collectionExists.value
    && !props.exclude?.some((c: Collection) => {
      const collectionName = getLocaleContent(c.name).toLowerCase()
      return collectionName === query
    })
})
</script>

<template>
  <div class="relative">
    <div class="pl-3 absolute flex items-center left-0 inset-y-0 pointer-events-none">
      <Search class="h-4 text-gray-400 w-4" />
    </div>
    <BaseInput
      v-model="searchQuery"
      type="text"
      class="pl-10 pr-10 py-2 w-full border focus:outline-none rounded-md border-gray-300 focus:ring-2 focus:ring-blue-500"
      placeholder="Search collections..."
    />
    <div v-if="isChecking" class="pr-3 absolute flex items-center right-0 inset-y-0">
      <Loader class="h-4 w-4 animate-spin text-gray-400" />
    </div>
    <button
      v-else-if="showCreateButton"
      class="pr-3 text-blue-500 hover:text-blue-700 absolute flex items-center right-0 inset-y-0"
      :disabled="isCreating"
      @click="handleCreateNewCollection"
    >
      <Loader v-if="isCreating" class="h-5 w-5 animate-spin" />
      <Plus v-else class="h-5 w-5" />
    </button>
  </div>
</template>
