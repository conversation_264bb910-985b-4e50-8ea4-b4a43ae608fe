export default function useAuth() {
  const { clientFetch } = useCustomFetch()
  const authStore = useAuthStore()
  const router = useRouter()
  const { getDefaultHeaders } = useCustomFetch()

  async function authGuard() {
    if (authStore.hasInit) {
      return true
    }

    const token = useCookie('token')
    if (!token.value) {
      return false
    }

    const user = await getUser()
    if (user) {
      authStore.initialize(user)
      return true
    }
  }

  async function tryLogin() {
    const token = useCookie('token')
    if (!token.value) {
      return
    }

    const user = await getUser()

    if (user) {
      authStore.initialize(user)
      router.push('/')
      return true
    }
    else {
      authStore.logout()
      return false
    }
  }

  async function getUser() {
    const { data } = await useAsyncData(async () => {
      const { data } = await $fetch<ResponseData<AuthState>>('/api/me', {
        headers: getDefaultHeaders(),
      })
      return data
    })

    return data.value
  }

  async function login(credentials: Credentials) {
    const data = await clientFetch<AuthState>('/api/login', {
      method: 'POST',
      body: {
        email: credentials.email,
        password: credentials.password,
      },
    })
    if (data && data.token) {
      saveTokenToCookie(data.token)
      authStore.initialize(data)
      return true
    }

    return false
  }

  function saveTokenToCookie(token: string) {
    const cookie = useCookie('token')
    cookie.value = token
  }

  return {
    login,
    tryLogin,
    authGuard,
  }
}
