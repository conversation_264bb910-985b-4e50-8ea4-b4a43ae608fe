<script setup>
import { cn } from '@/lib/utils'
import { X } from 'lucide-vue-next'

// <PERSON><PERSON><PERSON> nghĩa props
const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  title: {
    type: String,
    default: '',
  },
  description: {
    type: String,
    default: '',
  },
  className: {
    type: String,
    default: '',
  },
  showCloseButton: {
    type: Boolean,
    default: true,
  },
})

// Định nghĩa emits
const emit = defineEmits(['close'])

// Quản lý trạng thái
const overlayRef = ref(null)
const contentRef = ref(null)
const isMounted = ref(false)

// Xử lý vòng đời
onMounted(() => {
  isMounted.value = true
  if (props.isOpen) {
    document.body.style.overflow = 'hidden'
  }
})

onUnmounted(() => {
  document.body.style.overflow = ''
})

// Xử lý click bên ngoài
function handleOverlayClick(e) {
  if (overlayRef.value === e.target) {
    emit('close')
  }
}

watch(() => props.isOpen, (newVal) => {
  if (newVal && contentRef.value) {
    contentRef.value.focus()
  }
})
</script>

<template>
  <Teleport to="body">
    <div
      v-if="isMounted && isOpen"
      ref="overlayRef"
      class="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
      role="dialog"
      aria-modal="true"
      :aria-labelledby="title ? 'modal-title' : undefined"
      :aria-describedby="description ? 'modal-description' : undefined"
      @click="handleOverlayClick"
    >
      <div
        ref="contentRef"
        tabindex="-1"
        :class="cn(
          'animate-in fade-in-0 zoom-in-95 duration-200',
          'relative overflow-auto rounded-lg bg-background p-6 shadow-lg w-fit flex flex-col',
          className,
        )"
      >
        <BaseButton
          v-if="showCloseButton"
          variant="ghost"
          size="icon"
          class="absolute right-4 top-4"
          aria-label="Close dialog"
          @click="emit('close')"
        >
          <X class="h-4 w-4" />
        </BaseButton>

        <div class="border-b">
          <h2 v-if="title" id="modal-title" class="mb-2 text-xl font-semibold">
            {{ title }}
          </h2>
          <p v-if="description" id="modal-description" class="mb-6 text-muted-foreground">
            {{ description }}
          </p>
        </div>

        <div class="flex-1 overflow-auto">
          <slot />
        </div>

        <div class="border-t pt-4">
          <slot name="footer" />
        </div>
      </div>
    </div>
  </Teleport>
</template>
