<script setup lang="ts">
import type { SaleEventFormData } from '~/types/event'
import { Calendar, Info, Percent } from 'lucide-vue-next'

interface Props {
  form: SaleEventFormData
  errors?: Record<string, string>
  isSubmitting?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  errors: () => ({}),
  isSubmitting: false,
})

const emit = defineEmits<{
  (e: 'update:form', form: SaleEventFormData): void
  (e: 'submit'): void
}>()

// Local form state that syncs with parent
const localForm = computed({
  get: () => props.form,
  set: value => emit('update:form', value),
})

// Use refs for date inputs to ensure reactivity
const startDateInput = ref('')
const endDateInput = ref('')

// Initialize refs when props change
watchEffect(() => {
  if (props.form.start_date) {
    startDateInput.value = new Date(props.form.start_date).toISOString().split('T')[0]
  }
  if (props.form.end_date) {
    endDateInput.value = new Date(props.form.end_date).toISOString().split('T')[0]
  }
})

// Watch for changes to date inputs and emit updates
watch(startDateInput, (newValue) => {
  if (newValue !== null && newValue !== undefined) {
    // Update the specific property instead of replacing the whole object
    localForm.value.start_date = newValue ? `${newValue}T00:00:00.000000Z` : ''
  }
}, { immediate: false })

watch(endDateInput, (newValue) => {
  if (newValue !== null && newValue !== undefined) {
    // Update the specific property instead of replacing the whole object
    localForm.value.end_date = newValue ? `${newValue}T00:00:00.000000Z` : ''
  }
}, { immediate: false })

// Validation functions
function validateForm(): boolean {
  let isValid = true
  const newErrors: Record<string, string> = {}

  // Name validation
  if (!localForm.value.name?.trim()) {
    newErrors.name = 'Event name is required'
    isValid = false
  }

  // Date validation
  if (!localForm.value.start_date) {
    newErrors.start_date = 'Start date is required'
    isValid = false
  }

  if (!localForm.value.end_date) {
    newErrors.end_date = 'End date is required'
    isValid = false
  }

  if (localForm.value.start_date && localForm.value.end_date) {
    const start = new Date(localForm.value.start_date)
    const end = new Date(localForm.value.end_date)

    if (end <= start) {
      newErrors.end_date = 'End date must be after start date'
      isValid = false
    }
  }

  // Sale percent validation
  if (localForm.value.sale_percent < 0 || localForm.value.sale_percent > 100) {
    newErrors.sale_percent = 'Discount must be between 0 and 100'
    isValid = false
  }

  return isValid
}

// Calculate duration preview
const durationPreview = computed(() => {
  if (!localForm.value.start_date || !localForm.value.end_date)
    return ''

  const start = new Date(localForm.value.start_date)
  const end = new Date(localForm.value.end_date)
  const days = Math.floor((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1

  return `${days} day${days !== 1 ? 's' : ''}`
})

// Handle form submission
function handleSubmit() {
  if (validateForm()) {
    emit('submit')
  }
}

// Expose validation function to parent
defineExpose({
  validateForm,
})
</script>

<template>
  <form @submit.prevent="handleSubmit">
    <div class="space-y-6">
      <!-- Basic Information -->
      <BaseCard>
        <BaseCardContent class="space-y-6">
          <!-- Event Name -->
          <div>
            <BaseLabel for="event-name" required>
              Event Name
            </BaseLabel>
            <BaseInput
              id="event-name"
              v-model="localForm.name"
              placeholder="e.g., Summer Sale 2024, Black Friday Deal"
              :class="{ 'border-destructive': errors.name }"
            />
            <p v-if="errors.name" class="text-sm text-destructive mt-1">
              {{ errors.name }}
            </p>
          </div>

          <!-- Status -->
          <div class="flex items-center justify-between p-4 border rounded-lg">
            <div class="space-y-0.5">
              <BaseLabel>Event Status</BaseLabel>
              <p class="text-sm text-muted-foreground">
                Enable or disable this sale event
              </p>
            </div>
            <BaseSwitch v-model="localForm.status" />
          </div>
        </BaseCardContent>
      </BaseCard>

      <!-- Sale Configuration -->
      <BaseCard>
        <BaseCardHeader>
          <BaseCardTitle>Sale Configuration</BaseCardTitle>
        </BaseCardHeader>
        <BaseCardContent class="space-y-6">
          <!-- Date Range -->
          <div class="grid grid-cols-2 gap-4">
            <div>
              <BaseLabel for="start-date" required>
                Start Date
              </BaseLabel>
              <div class="relative">
                <Calendar class="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <BaseInput
                  id="start-date"
                  v-model="startDateInput"
                  type="date"
                  class="pl-10"
                  :class="{ 'border-destructive': errors.start_date }"
                />
              </div>
              <p v-if="errors.start_date" class="text-sm text-destructive mt-1">
                {{ errors.start_date }}
              </p>
            </div>

            <div>
              <BaseLabel for="end-date" required>
                End Date
              </BaseLabel>
              <div class="relative">
                <Calendar class="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <BaseInput
                  id="end-date"
                  v-model="endDateInput"
                  type="date"
                  class="pl-10"
                  :class="{ 'border-destructive': errors.end_date }"
                />
              </div>
              <p v-if="errors.end_date" class="text-sm text-destructive mt-1">
                {{ errors.end_date }}
              </p>
            </div>
          </div>

          <!-- Duration Preview -->
          <div v-if="durationPreview" class="flex items-center gap-2 text-sm text-muted-foreground">
            <Info class="h-4 w-4" />
            <span>Event duration: {{ durationPreview }}</span>
          </div>

          <!-- Discount Percentage -->
          <div>
            <BaseLabel for="sale-percent" required>
              Discount Percentage
            </BaseLabel>
            <div class="relative max-w-xs">
              <Percent class="absolute right-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <BaseInput
                id="sale-percent"
                v-model.number="localForm.sale_percent"
                type="number"
                min="0"
                max="100"
                step="0.01"
                placeholder="0"
                class="pr-10"
                :class="{ 'border-destructive': errors.sale_percent }"
              />
            </div>
            <p v-if="errors.sale_percent" class="text-sm text-destructive mt-1">
              {{ errors.sale_percent }}
            </p>
            <p class="text-sm text-muted-foreground mt-1">
              Set the discount percentage (0-100) that will apply to all products in this sale
            </p>
          </div>
        </BaseCardContent>
      </BaseCard>

      <!-- Action Buttons Slot -->
      <slot name="actions" :is-submitting="isSubmitting" :validate="validateForm" />
    </div>
  </form>
</template>
