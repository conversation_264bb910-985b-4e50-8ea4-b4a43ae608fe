<script lang="ts" setup>
defineProps<{
  tooltip: string
  delay?: number
  cursorNormal?: boolean
}>()
</script>

<template>
  <ClientOnly>
    <template #default>
      <div>
        <BaseTooltipProvider :delay-duration="delay ?? 0.1">
          <BaseTooltip>
            <BaseTooltipTrigger :class="{ 'cursor-default': cursorNormal }">
              <slot />
            </BaseTooltipTrigger>
            <BaseTooltipContent>
              <p>{{ tooltip }}</p>
            </BaseTooltipContent>
          </BaseTooltip>
        </BaseTooltipProvider>
      </div>
    </template>
    <template #fallback>
      <slot />
    </template>
  </ClientOnly>
</template>
