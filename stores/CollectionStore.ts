interface CollectionStore {
  pool: Collection[]
}

export const useCollectionStore = defineStore('collections', {
  state: () => ({
    pool: [],
  } as CollectionStore),
  actions: {
    async fetch(force = false) {
      if (!force && this.pool.length) {
        return
      }

      const { clientFetch } = useCustomFetch()
      const collections = await clientFetch<Collection[]>('/api/collections', {
        method: 'GET',
      })

      if (collections) {
        this.pool = collections
      }
    },
  },
})
