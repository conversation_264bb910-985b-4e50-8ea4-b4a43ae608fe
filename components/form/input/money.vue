<script setup lang="ts">
import type { InputProps } from '@/types/component'
import { useInputLogic } from './logic'

interface Props extends InputProps {
  modelValue: number | null | string
  placeholder?: string
  currency?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Enter amount',
  currency: '$',
  disabled: false,
})

const emit = defineEmits<{
  'update:modelValue': [value: number | null]
}>()

const { innerValue, inputClass, style } = useInputLogic(props, emit)

const currency = computed(() => {
  switch (props.currency) {
    case 'USD':
      return '$'
    case 'EUR':
      return '€'
    case 'GBP':
      return '£'
    case 'VND':
      return '₫'
    default:
      return '$'
  }
})
</script>

<template>
  <div>
    <div class="relative">
      <span
        class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500"
        :class="{
          'text-xl': currency === '₫',
        }"
      >
        {{ currency }}
      </span>
      <BaseInput
        v-model="innerValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :style="style"
        :class="inputClass"
        class="pl-7 max-w-40"
        type="number"
        inputmode="decimal"
      />
    </div>
  </div>
</template>
