import { define<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'h3'
import { defaultHeader } from '~/config/client'
import { replaceUrl } from '../utils/proxy'

const DEBUG = false

export default defineEventHandler(async (event) => {
  const path = replaceUrl(event.path)
  if (DEBUG) {
    console.log('path', path)
  }
  const cookies = parseCookies(event)
  const token = cookies.token
  const headers = token ? { Authorization: `Bearer ${token}` } : {}
  const filteredHeaders = Object.fromEntries(
    [...event.headers.entries()].filter(([key]) => key.toLowerCase() !== 'connection'),
  )
  const payload = {
    method: event.method,
    headers: {
      ...defaultHeader,
      ...headers,
      ...filteredHeaders,
    },
    query: getQuery(event),
    body: event.method === 'GET' ? undefined : await readRawBody(event),
    ignoreResponseError: true,
  }
  if (DEBUG) {
    console.log('payload', payload)
  }

  const response = await $fetch(path, payload) as Response
  if (response.status >= 200 && response.status < 300) {
    setResponseHeader(event, 'Content-Type', 'application/json')
    return response
  }

  console.error('error from server', response)

  setResponseStatus(event, 500)
  setResponseHeader(event, 'Content-Type', 'text/html')

  return response
})
