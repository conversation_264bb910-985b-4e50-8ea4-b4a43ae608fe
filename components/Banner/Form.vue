<script setup lang="ts">
import type { BannerForm } from '@/types/banner'
import useBanner from '@/composables/custom/banner'
import { Trash } from 'lucide-vue-next'

const { notifyError } = useToast()
const { uploadTemp } = useUpload()
const { initBanner } = useBanner()

const form = defineModel('form', {
  type: Object as PropType<{
    banners: BannerForm[]
  }>,
  required: true,
})

function addBanner() {
  form.value.banners.push(initBanner())
}

async function previewFile(banner: BannerForm, files: FileList, key: 'file' | 'file_mobile') {
  const file = files[0]

  const type = getFileType(file)
  if (!type) {
    notifyError('File type is not supported')
    return
  }

  const url = await uploadTemp(files)
  if (url) {
    banner[key] = {
      url: url[0],
      type,
      id: null,
    }
  }
}

function removeBanner(banner: BannerForm) {
  const index = form.value.banners.indexOf(banner)
  if (index !== -1) {
    form.value.banners.splice(index, 1)
  }
}
</script>

<template>
  <div>
    <draggable v-model="form.banners" item-key="id" class="space-y-16">
      <template #item="{ element: banner }">
        <div class="space-y-3 border border-gray-200 rounded-md px-4 py-2 relative w-fit min-w-[600px]">
          <div class="absolute -top-4 -right-4">
            <BaseButton size="sm" variant="danger" @click="removeBanner(banner)">
              <Trash name="icons:trash" class="w-5 h-5 text-white" />
            </BaseButton>
          </div>
          <FormInput
            id="url"
            v-model="banner.url"
            placeholder="URL"
          />
          <div>
            <p>Desktop</p>
            <FormUpload
              v-if="!banner.file"
              v-model="banner.file"
              accept="image/*,video/*"
              @on-file-upload="(files) => previewFile(banner, files, 'file')"
            />
            <FilePreview v-if="banner.file" :file="banner.file" />
          </div>
          <div>
            <p>Mobile</p>
            <FormUpload
              v-if="!banner.file_mobile"
              v-model="banner.file_mobile"
              accept="image/*,video/*"
              @on-file-upload="(files) => previewFile(banner, files, 'file_mobile')"
            />
            <FilePreview v-if="banner.file_mobile" :file="banner.file_mobile" />
          </div>
        </div>
      </template>
    </draggable>
    <BaseButton class="mt-6" size="sm" @click="addBanner">
      Add Banner
    </BaseButton>
  </div>
</template>
