<script lang="ts" setup>
import type { BannerDetail } from '@/types/banner'
import { Pencil, PlusCircle, Trash } from 'lucide-vue-next'

definePageMeta({
  middleware: ['auth'],
})

const { getDefaultHeaders, clientFetch } = useCustomFetch()
const { withConfirm } = useConfirm()
const { notifySuccess } = useToast()
const { data: banners, refresh } = await useAsyncData('banners', async () => {
  const { data } = await $fetch<ResponseData<BannerDetail[][]>>('/api/banners', {
    headers: getDefaultHeaders(),
  })

  return data
})

async function doDelete(group: number) {
  withConfirm({
    title: 'Delete Collection',
    description: 'Are you sure you want to delete this collection?',
    onConfirm: async () => {
      await deleteBannerRequest(group)
    },
  })
}

async function deleteBannerRequest(id: number) {
  const success = await clientFetch<ResponseData<boolean>>(`/api/banners/${id}`, {
    method: 'DELETE',
  })

  if (success) {
    notifySuccess('Banner deleted successfully')
  }
}

async function onChangePosition(data: any) {
  const { newIndex, element } = data.moved
  const success = await clientFetch<ResponseData<boolean>>(`/api/banners/change-position/${element[0].group}`, {
    method: 'PUT',
    body: {
      new_group: newIndex,
    },
  })

  if (success) {
    notifySuccess('Banner position changed successfully')
    refresh()
  }
}
</script>

<template>
  <div>
    <div class="flex justify-end my-2">
      <NuxtLink to="/banners/create">
        <BaseButton size="xs" class="px-4">
          <PlusCircle class="w-3 h-3" />Create
        </BaseButton>
      </NuxtLink>
    </div>
    <BaseCard>
      <BaseCardHeader>
        <BaseCardTitle>Banners</BaseCardTitle>
      </BaseCardHeader>
      <BaseCardContent>
        <draggable
          v-model="banners"
          group="banners"
          item-key="id"
          class="space-y-16"
          @change="onChangePosition"
        >
          <template #item="{ element: group }">
            <div class="flex justify-between  border border-gray-200 py-2 px-2 rounded">
              <div class="space-y-3 rounded-md px-4 relative min-w-[600px] flex items-center max-h-40 overflow-hidden gap-2">
                <div v-for="banner in group" :key="banner.id">
                  <FilePreview
                    :file="{
                      url: banner.file.path,
                      type: banner.file.type,
                    }"
                  />
                </div>
              </div>
              <div class="flex items-center">
                <div class="flex flex-col gap-4">
                  <NuxtLink :to="`/banners/edit/${group[0].group}`">
                    <BaseButton size="sm">
                      <Pencil class="w-4 h-4" />
                    </BaseButton>
                  </NuxtLink>
                  <BaseButton
                    class="p-2 rounded flex items-center"
                    variant="outlineDanger"
                    size="sm"
                    @click="doDelete(group[0].group)"
                  >
                    <Trash class="w-4 h-4" />
                  </BaseButton>
                </div>
              </div>
            </div>
          </template>
        </draggable>
      </BaseCardContent>
    </BaseCard>
  </div>
</template>
