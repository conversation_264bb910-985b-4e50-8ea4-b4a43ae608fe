{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "lint": "eslint .", "postinstall": "nuxt prepare", "cache:clear": "npx nuxi cleanup", "serve": "node --env-file=.env .output/server/index.mjs"}, "dependencies": {"@nuxtjs/color-mode": "^3.5.2", "@pinia/nuxt": "^0.9.0", "@tinymce/tinymce-vue": "^6.1.0", "@vueuse/core": "^12.8.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "js-beautify": "^1.15.3", "lucide-vue-next": "^0.471.0", "nuxt": "^3.15.1", "nuxt-tiptap-editor": "^2.1.4", "pinia": "^2.3.0", "reka-ui": "^2.3.1", "shadcn-nuxt": "^0.11.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vue": "latest", "vue-router": "latest", "vuedraggable": "^4.1.0"}, "devDependencies": {"@antfu/eslint-config": "^3.14.0", "@nuxt/eslint": "^0.7.4", "@nuxtjs/tailwindcss": "^6.13.1", "@types/js-beautify": "^1.14.3", "eslint": "^9.17.0", "eslint-plugin-format": "^1.0.1"}}