<script lang="ts" setup>
import { PlusCircle } from 'lucide-vue-next'

const options = defineModel('options', {
  type: Object as PropType<ProductOptions>,
  required: true,
})
</script>

<template>
  <BaseCard>
    <BaseCardHeader>
      Options
    </BaseCardHeader>
    <BaseCardContent>
      <div>
        <div class="flex gap-2">
          <ColorCircle
            v-for="color in options.colors"
            :key="color"
            :color="color"
            class="w-8 h-8"
          />
          <div>
            <ColorModalSelect v-model:colors="options.colors">
              <Tooltip tooltip="Add Color">
                <PlusCircle class="w-9 h-9 text-gray-700" />
              </Tooltip>
            </ColorModalSelect>
          </div>
        </div>
        <FormInputTags
          v-model="options.sizes"
          label="Sizes"
          placeholder="Sizes"
        />
      </div>
    </BaseCardContent>
  </BaseCard>
</template>
