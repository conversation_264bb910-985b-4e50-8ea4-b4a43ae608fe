import { useEventBus } from '@vueuse/core'

const eventBus = useEventBus<string>('confirm-alert')

interface ConfirmOptions {
  title: string
  description: string
  onConfirm: () => void
}

export function useConfirm() {
  function withConfirm(options: ConfirmOptions) {
    const { title, description, onConfirm } = options
    eventBus.emit('show-confirm', {
      title: title || 'Confirm',
      description: description || 'This action cannot be undone.',
      onConfirm: () => {
        onConfirm()
      },
    })
  }

  return {
    withConfirm,
  }
}
