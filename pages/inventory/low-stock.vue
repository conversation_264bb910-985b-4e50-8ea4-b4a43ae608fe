<script setup lang="ts">
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Button } from '~/components/ui/button'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'
import { Alert, AlertDescription } from '~/components/ui/alert'
import StockStatus from '~/components/Inventory/StockStatus.vue'
import StockManager from '~/components/Inventory/StockManager.vue'
import { AlertTriangle, RefreshCw, Settings } from 'lucide-vue-next'

definePageMeta({
  layout: 'default'
})

const { getDefaultHeaders } = useCustomFetch()
const { notifyError } = useToast()

// Reactive data
const loading = ref(false)
const threshold = ref(5)
const lowStockVariants = ref<Variant[]>([])

// Methods
async function fetchLowStockVariants() {
  try {
    loading.value = true
    
    const response = await $fetch(`/api/v1/admin/variants/low-stock?threshold=${threshold.value}`, {
      headers: getDefaultHeaders()
    })
    
    lowStockVariants.value = response.data || []
  } catch (error) {
    console.error('Failed to fetch low stock variants:', error)
    notifyError('Failed to load low stock variants')
  } finally {
    loading.value = false
  }
}

function onStockUpdated(updatedVariant: Variant) {
  const index = lowStockVariants.value.findIndex(v => v.id === updatedVariant.id)
  if (index !== -1) {
    // If stock is now above threshold, remove from list
    if ((updatedVariant.quantity || 0) > threshold.value) {
      lowStockVariants.value.splice(index, 1)
    } else {
      lowStockVariants.value[index] = updatedVariant
    }
  }
}

// Watch threshold changes
watch(threshold, () => {
  fetchLowStockVariants()
})

// Lifecycle
onMounted(() => {
  fetchLowStockVariants()
})
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight flex items-center gap-2">
          <AlertTriangle class="h-8 w-8 text-yellow-600" />
          Low Stock Variants
        </h1>
        <p class="text-muted-foreground">
          Variants that need attention and restocking
        </p>
      </div>
      <Button @click="fetchLowStockVariants" :disabled="loading">
        <RefreshCw class="h-4 w-4 mr-2" :class="{ 'animate-spin': loading }" />
        Refresh
      </Button>
    </div>

    <!-- Alert if variants found -->
    <Alert v-if="lowStockVariants.length > 0" class="border-yellow-200 bg-yellow-50">
      <AlertTriangle class="h-4 w-4 text-yellow-600" />
      <AlertDescription class="text-yellow-800">
        <strong>{{ lowStockVariants.length }}</strong> variant{{ lowStockVariants.length > 1 ? 's' : '' }} 
        {{ lowStockVariants.length > 1 ? 'are' : 'is' }} running low on stock and may need restocking soon.
      </AlertDescription>
    </Alert>

    <!-- Settings Card -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <Settings class="h-5 w-5" />
          Low Stock Settings
        </CardTitle>
        <CardDescription>Configure the threshold for low stock alerts</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="flex items-center gap-4">
          <div class="space-y-2">
            <Label for="threshold">Low Stock Threshold</Label>
            <Input
              id="threshold"
              v-model.number="threshold"
              type="number"
              min="1"
              max="50"
              class="w-32"
            />
          </div>
          <div class="text-sm text-muted-foreground">
            Variants with stock at or below this number will be considered low stock
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- Low Stock Variants Table -->
    <Card>
      <CardHeader>
        <CardTitle>Low Stock Variants ({{ lowStockVariants.length }})</CardTitle>
        <CardDescription>
          Showing variants with {{ threshold }} or fewer units in stock
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div v-if="loading" class="flex items-center justify-center py-8">
          <RefreshCw class="h-6 w-6 animate-spin mr-2" />
          Loading low stock variants...
        </div>
        
        <div v-else-if="lowStockVariants.length === 0" class="text-center py-8">
          <AlertTriangle class="h-12 w-12 text-green-600 mx-auto mb-4" />
          <h3 class="text-lg font-semibold text-green-600 mb-2">Great News!</h3>
          <p class="text-muted-foreground">
            No variants are currently low on stock. All variants have more than {{ threshold }} units available.
          </p>
        </div>
        
        <Table v-else>
          <TableHeader>
            <TableRow>
              <TableHead>SKU</TableHead>
              <TableHead>Size</TableHead>
              <TableHead>SAPO SKU</TableHead>
              <TableHead class="text-center">Current Stock</TableHead>
              <TableHead class="text-center">Status</TableHead>
              <TableHead class="text-center">Days Until Out</TableHead>
              <TableHead class="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow v-for="variant in lowStockVariants" :key="variant.id" class="border-l-4 border-l-yellow-400">
              <TableCell class="font-medium">{{ variant.sku }}</TableCell>
              <TableCell>{{ variant.size }}</TableCell>
              <TableCell>{{ variant.sapo_sku || '-' }}</TableCell>
              <TableCell class="text-center">
                <span class="font-mono text-lg font-semibold text-yellow-600">
                  {{ variant.quantity || 0 }}
                </span>
              </TableCell>
              <TableCell class="text-center">
                <StockStatus :quantity="variant.quantity || 0" :low-stock-threshold="threshold" />
              </TableCell>
              <TableCell class="text-center">
                <span class="text-sm text-muted-foreground">
                  <!-- This could be calculated based on sales velocity if available -->
                  Estimate needed
                </span>
              </TableCell>
              <TableCell class="text-right">
                <StockManager :variant="variant" @stock-updated="onStockUpdated" />
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>

    <!-- Quick Actions -->
    <Card v-if="lowStockVariants.length > 0">
      <CardHeader>
        <CardTitle>Quick Actions</CardTitle>
        <CardDescription>Bulk operations for low stock variants</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="flex gap-2">
          <Button variant="outline" disabled>
            <AlertTriangle class="h-4 w-4 mr-2" />
            Export Low Stock Report
          </Button>
          <Button variant="outline" disabled>
            <RefreshCw class="h-4 w-4 mr-2" />
            Bulk Restock
          </Button>
        </div>
        <p class="text-sm text-muted-foreground mt-2">
          Bulk operations coming soon. Use individual stock managers for now.
        </p>
      </CardContent>
    </Card>
  </div>
</template>
