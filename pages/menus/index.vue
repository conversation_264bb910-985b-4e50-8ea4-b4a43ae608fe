<script setup lang="ts">
import type { MenuGroup, MenuItem } from '~/types/menu'
import { Plus } from 'lucide-vue-next'

definePageMeta({
  middleware: ['auth'],
})

const { getDefaultHeaders, clientFetch } = useCustomFetch()

const { data: fetchedMenus, refresh } = await useAsyncData(async () => {
  const { data } = await $fetch<ResponseData<MenuGroup[]>>('/api/menu', {
    headers: getDefaultHeaders(),
  })

  return data
})

const columns = ref(fetchedMenus.value?.map(menu => (mapData(menu))) || [])

function mapData(data: MenuGroup) {
  return {
    id: data.id,
    title: data.title,
    total: data.total,
    slug: data.slug,
    items: data.menu_items.map(item => ({
      id: item.id,
      title: item.title,
      position: item.position,
      total: item.total,
      slug: item.slug,
      collections: item.collections || [],
    })),
    position: data.position,
  }
}

async function refreshData() {
  await refresh()
  columns.value = fetchedMenus.value?.map(menu => (mapData(menu))) || []
}

async function onDeleteColumn(id: number) {
  const success = await clientFetch(`/api/menu/${id}`, {
    method: 'DELETE',
  })
  if (success) {
    useToast().notifySuccess('Menu deleted successfully')
    const columnIndex = columns.value.findIndex(column => column.id === id)
    columns.value.splice(columnIndex, 1)
  }
}

async function addMenu() {
  const success = await clientFetch<MenuGroup>('/api/menu', {
    method: 'POST',
  })

  if (success) {
    useToast().notifySuccess('Menu added successfully')
    await refreshData()
  }
}

async function addItemToGroup(column: any) {
  const item = await clientFetch<MenuItem>(`/api/menu-items/${column.id}`, {
    method: 'POST',
  })

  if (item) {
    column.items.push({
      id: item.id,
      title: 'New Item',
    })

    useToast().notifySuccess('Item added successfully')
  }
}

async function onChangeMenuPosition(data: any) {
  const oldIndex = data.moved.oldIndex
  const target = columns.value[oldIndex]
  const menu = data.moved.element
  const success = await clientFetch(`/api/menu/update-position/${menu.id}`, {
    method: 'PUT',
    body: {
      old_position: menu.position,
      new_position: target.position,
    },
  })

  if (success) {
    useToast().notifySuccess('Menu position updated successfully')
  }
  else {
    useToast().notifyError('Failed to update menu position')
    refreshData()
  }
}
</script>

<template>
  <div class="min-h-screen bg-gray-50 px-6 py-8">
    <div class="max-w-7xl mx-auto">
      <!-- Header Section -->
      <div class="mb-8">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900 mb-2">
              Menu Management
            </h1>
            <p class="text-sm text-gray-600">
              Organize and manage your navigation menus
            </p>
          </div>
          <BaseButton
            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg shadow-sm transition-colors duration-200 flex items-center gap-2"
            @click="addMenu"
          >
            <Plus class="h-4 w-4" />
            Add Menu
          </BaseButton>
        </div>
      </div>

      <!-- Main Content -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        <div class="p-6">
          <div v-if="columns.length === 0" class="text-center py-16">
            <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
              <Plus class="h-8 w-8 text-gray-400" />
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">
              No menus yet
            </h3>
            <p class="text-gray-500 mb-6">
              Get started by creating your first navigation menu
            </p>
            <BaseButton
              class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg shadow-sm transition-colors duration-200"
              @click="addMenu"
            >
              Create Your First Menu
            </BaseButton>
          </div>

          <client-only v-else>
            <div class="overflow-x-auto">
              <draggable
                v-model="columns"
                group="columns"
                item-key="id"
                :animation="200"
                ghost-class="ghost"
                class="flex gap-6 pb-4 min-h-[400px]"
                @change="onChangeMenuPosition"
              >
                <template #item="{ element: column }">
                  <div class="w-80 flex-shrink-0">
                    <NavigationColumnHeader
                      :column="column"
                      class="mb-3"
                      @delete="onDeleteColumn(column.id)"
                    />

                    <!-- Menu Items Container -->
                    <div class="bg-gray-50 border-2 border-dashed border-gray-200 rounded-xl p-4 min-h-[300px] transition-colors duration-200 hover:border-gray-300">
                      <NavigationItem
                        v-model:items="column.items"
                        :menu-id="column.id"
                        @refresh="refreshData"
                      />

                      <!-- Add Item Button -->
                      <button
                        class="w-full mt-4 flex items-center justify-center gap-2 py-3 px-4 text-sm text-gray-600 hover:text-gray-900 hover:bg-white rounded-lg border-2 border-dashed border-gray-300 hover:border-gray-400 transition-all duration-200 group"
                        @click="addItemToGroup(column)"
                      >
                        <Plus class="h-4 w-4 group-hover:scale-110 transition-transform duration-200" />
                        <span class="font-medium">Add New Item</span>
                      </button>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>
          </client-only>
        </div>
      </div>
    </div>

    <NavigationModalEditItem
      @refresh="refreshData"
    />
  </div>
</template>

<style scoped>
.ghost {
  opacity: 0.6;
  background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
  transform: rotate(2deg);
  border: 2px dashed #0891b2;
  border-radius: 12px;
}

/* Smooth scrollbar for horizontal scroll */
.overflow-x-auto::-webkit-scrollbar {
  height: 6px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>
