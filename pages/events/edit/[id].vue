<script lang="ts" setup>
import type { SaleEvent, SaleEventFormData } from '~/types/event'
import { BarChart3, DollarSign, Package, Percent, Settings, Trash2 } from 'lucide-vue-next'

definePageMeta({
  middleware: ['auth'],
})

const route = useRoute()
const router = useRouter()
const eventId = route.params.id as string
const { clientFetch, getDefaultHeaders } = useCustomFetch()
const { notifySuccess, notifyError } = useToast()
const { withConfirm } = useConfirm()

// Fetch sale event data
const { data: saleEvent, refresh } = await useAsyncData(`sale-event-${eventId}`, async () => {
  const { data } = await $fetch<ResponseData<SaleEvent>>(`/api/sale-events/${eventId}`, {
    headers: getDefaultHeaders(),
  })
  return data
})

if (!saleEvent.value) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Sale event not found',
  })
}

// Initialize form data with the sale event data
const form = reactive<SaleEventFormData>({
  name: saleEvent.value.name,
  start_date: saleEvent.value.start_date,
  end_date: saleEvent.value.end_date,
  sale_percent: saleEvent.value.sale_percent,
  status: saleEvent.value.status,
  description: saleEvent.value.description || '',
})

// Tab state
const activeTab = ref('details')

// Validation state
const errors = reactive({
  name: '',
  start_date: '',
  end_date: '',
  sale_percent: '',
})

// Loading states
const isUpdating = ref(false)
const isDeleting = ref(false)

// Validation functions
function validateForm(): boolean {
  let isValid = true

  // Reset errors
  Object.keys(errors).forEach(key => errors[key as keyof typeof errors] = '')

  // Name validation
  if (!form.name.trim()) {
    errors.name = 'Event name is required'
    isValid = false
  }

  // Date validation
  if (!form.start_date) {
    errors.start_date = 'Start date is required'
    isValid = false
  }

  if (!form.end_date) {
    errors.end_date = 'End date is required'
    isValid = false
  }

  if (form.start_date && form.end_date) {
    const start = new Date(form.start_date)
    const end = new Date(form.end_date)

    if (end <= start) {
      errors.end_date = 'End date must be after start date'
      isValid = false
    }
  }

  // Sale percent validation
  if (form.sale_percent < 0 || form.sale_percent > 100) {
    errors.sale_percent = 'Discount must be between 0 and 100'
    isValid = false
  }

  return isValid
}

// Update sale event
async function updateSaleEvent() {
  if (!validateForm()) {
    notifyError('Please fix the errors in the form')
    return
  }

  isUpdating.value = true

  try {
    const success = await clientFetch(`/api/sale-events/${eventId}`, {
      method: 'PUT',
      body: form,
    })

    if (success) {
      notifySuccess('Sale event updated successfully')
      router.push(`/events`)
    }
    else {
      notifyError('Failed to update sale event')
    }
  }
  catch (error) {
    console.error('Error updating sale event:', error)
    notifyError('Failed to update sale event')
  }
  finally {
    isUpdating.value = false
  }
}

// Delete sale event
async function deleteSaleEvent() {
  if (!saleEvent.value?.statistics?.can_delete) {
    notifyError('Cannot delete sale event because it has products that have been ordered')
    return
  }

  withConfirm({
    title: 'Delete Sale Event',
    description: `Are you sure you want to delete "${form.name}"? This action cannot be undone.`,
    onConfirm: async () => {
      isDeleting.value = true

      try {
        const success = await clientFetch(`/api/sale-events/${eventId}`, {
          method: 'DELETE',
        })

        if (success) {
          notifySuccess('Sale event deleted successfully')
          router.push('/events')
        }
        else {
          notifyError('Failed to delete sale event')
        }
      }
      catch (error) {
        console.error('Error deleting sale event:', error)
        notifyError('Failed to delete sale event')
      }
      finally {
        isDeleting.value = false
      }
    },
  })
}

// Status helpers
function getStatusVariant() {
  if (!saleEvent.value)
    return 'secondary'
  if (!saleEvent.value.status)
    return 'secondary'
  if (saleEvent.value.is_active)
    return 'default'
  if (saleEvent.value.is_upcoming)
    return 'secondary'
  if (saleEvent.value.is_expired)
    return 'destructive'
  return 'secondary'
}

function getStatusText() {
  if (!saleEvent.value)
    return 'Unknown'
  if (!saleEvent.value.status)
    return 'Inactive'
  if (saleEvent.value.is_active)
    return 'Active'
  if (saleEvent.value.is_upcoming)
    return 'Upcoming'
  if (saleEvent.value.is_expired)
    return 'Expired'
  return 'Inactive'
}
</script>

<template>
  <div>
    <!-- Header -->
    <div class="flex items-center justify-between mb-6">
      <div>
        <div class="flex items-center gap-3">
          <h1 class="text-2xl font-bold">
            {{ form.name }}
          </h1>
          <BaseBadge :variant="getStatusVariant()">
            {{ getStatusText() }}
          </BaseBadge>
        </div>
        <p class="text-muted-foreground mt-1">
          Manage your sale event details and products
        </p>
      </div>

      <div class="flex items-center gap-3">
        <BaseButton
          variant="destructive"
          :disabled="isDeleting || !saleEvent?.statistics?.can_delete"
          @click="deleteSaleEvent"
        >
          <Trash2 class="h-4 w-4 mr-2" />
          Delete
        </BaseButton>
        <GoBack />
      </div>
    </div>

    <!-- Statistics Cards -->
    <div v-if="saleEvent?.statistics" class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <BaseCard>
        <BaseCardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-muted-foreground">
                Total Products
              </p>
              <p class="text-2xl font-bold">
                {{ saleEvent.statistics.total_products }}
              </p>
            </div>
            <Package class="h-8 w-8 text-muted-foreground" />
          </div>
        </BaseCardContent>
      </BaseCard>

      <BaseCard>
        <BaseCardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-muted-foreground">
                Ordered Products
              </p>
              <p class="text-2xl font-bold">
                {{ saleEvent.statistics.ordered_products }}
              </p>
            </div>
            <BarChart3 class="h-8 w-8 text-muted-foreground" />
          </div>
        </BaseCardContent>
      </BaseCard>

      <BaseCard>
        <BaseCardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-muted-foreground">
                Total Revenue
              </p>
              <p class="text-2xl font-bold">
                {{ formatPrice(saleEvent.statistics.total_revenue) }}
              </p>
            </div>
            <DollarSign class="h-8 w-8 text-muted-foreground" />
          </div>
        </BaseCardContent>
      </BaseCard>

      <BaseCard>
        <BaseCardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-muted-foreground">
                Discount
              </p>
              <p class="text-2xl font-bold">
                {{ saleEvent.sale_percent }}%
              </p>
            </div>
            <Percent class="h-8 w-8 text-muted-foreground" />
          </div>
        </BaseCardContent>
      </BaseCard>
    </div>

    <!-- Tabs -->
    <BaseTabs v-model="activeTab" class="w-full">
      <BaseTabsList class="grid w-full grid-cols-2 max-w-md">
        <BaseTabsTrigger value="details">
          <Settings class="h-4 w-4 mr-2" />
          Details
        </BaseTabsTrigger>
        <BaseTabsTrigger value="products">
          <Package class="h-4 w-4 mr-2" />
          Products
        </BaseTabsTrigger>
      </BaseTabsList>

      <!-- Details Tab -->
      <BaseTabsContent value="details" class="mt-6">
        <EventsForm
          v-model:form="form"
          :errors="errors"
          :is-submitting="isUpdating"
          @submit="updateSaleEvent"
        >
          <template #actions="{ isSubmitting: formSubmitting }">
            <div class="flex items-center justify-end gap-4">
              <!-- Save Indicator -->
              <div v-if="isUpdating" class="flex items-center gap-2 text-sm text-muted-foreground">
                <div class="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                <span>Saving changes...</span>
              </div>

              <!-- Save Button -->
              <BaseButton
                type="submit"
                :disabled="formSubmitting || isUpdating"
                class="ml-auto"
              >
                <template v-if="formSubmitting || isUpdating">
                  <div class="flex items-center gap-2">
                    <div class="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    Saving...
                  </div>
                </template>
                <template v-else>
                  Save Changes
                </template>
              </BaseButton>
            </div>
          </template>
        </EventsForm>
      </BaseTabsContent>

      <!-- Products Tab -->
      <BaseTabsContent value="products" class="mt-6">
        <EventsProductSelector
          :event-id="eventId"
          @products-updated="refresh"
        />
      </BaseTabsContent>
    </BaseTabs>
  </div>
</template>
