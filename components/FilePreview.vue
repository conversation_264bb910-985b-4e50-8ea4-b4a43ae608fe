<script setup lang="ts">
defineProps<{
  file: {
    url: string
    type: FileType
  }
}>()
</script>

<template>
  <div>
    <div v-if="file?.type === 'image'">
      <img :src="fileUrl(file.url)" alt="Banner" class="object-cover max-h-48">
    </div>
    <div v-else-if="file?.type === 'video'">
      <video
        :src="fileUrl(file.url)"
        class="object-cover max-h-96"
        controls
      />
    </div>
  </div>
</template>
