<script setup lang="ts">
import { Alert, AlertDescription } from '~/components/ui/alert'
import { Button } from '~/components/ui/button'
import { Badge } from '~/components/ui/badge'
import { AlertTriangle, X, ExternalLink } from 'lucide-vue-next'

interface Props {
  lowStockCount: number
  outOfStockCount: number
  dismissible?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  dismissible: true
})

const emit = defineEmits<{
  dismiss: []
}>()

const isDismissed = ref(false)

const shouldShow = computed(() => {
  return !isDismissed.value && (props.lowStockCount > 0 || props.outOfStockCount > 0)
})

const alertVariant = computed(() => {
  if (props.outOfStockCount > 0) return 'destructive'
  if (props.lowStockCount > 0) return 'default'
  return 'default'
})

const alertMessage = computed(() => {
  if (props.outOfStockCount > 0 && props.lowStockCount > 0) {
    return `${props.outOfStockCount} variant${props.outOfStockCount > 1 ? 's are' : ' is'} out of stock and ${props.lowStockCount} variant${props.lowStockCount > 1 ? 's are' : ' is'} running low`
  } else if (props.outOfStockCount > 0) {
    return `${props.outOfStockCount} variant${props.outOfStockCount > 1 ? 's are' : ' is'} completely out of stock`
  } else if (props.lowStockCount > 0) {
    return `${props.lowStockCount} variant${props.lowStockCount > 1 ? 's are' : ' is'} running low on stock`
  }
  return ''
})

function dismiss() {
  isDismissed.value = true
  emit('dismiss')
}
</script>

<template>
  <Alert 
    v-if="shouldShow" 
    :class="[
      'relative',
      outOfStockCount > 0 ? 'border-red-200 bg-red-50' : 'border-yellow-200 bg-yellow-50'
    ]"
  >
    <AlertTriangle 
      class="h-4 w-4" 
      :class="outOfStockCount > 0 ? 'text-red-600' : 'text-yellow-600'" 
    />
    
    <AlertDescription 
      :class="outOfStockCount > 0 ? 'text-red-800' : 'text-yellow-800'"
    >
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
          <span class="font-medium">Inventory Alert:</span>
          <span>{{ alertMessage }}</span>
          
          <div class="flex gap-1">
            <Badge 
              v-if="outOfStockCount > 0" 
              variant="destructive" 
              class="text-xs"
            >
              {{ outOfStockCount }} Out of Stock
            </Badge>
            <Badge 
              v-if="lowStockCount > 0" 
              variant="secondary" 
              class="text-xs bg-yellow-100 text-yellow-800 hover:bg-yellow-100"
            >
              {{ lowStockCount }} Low Stock
            </Badge>
          </div>
        </div>
        
        <div class="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            as-child
            :class="outOfStockCount > 0 ? 'border-red-300 text-red-700 hover:bg-red-100' : 'border-yellow-300 text-yellow-700 hover:bg-yellow-100'"
          >
            <NuxtLink to="/inventory">
              <ExternalLink class="h-3 w-3 mr-1" />
              View Inventory
            </NuxtLink>
          </Button>
          
          <Button
            v-if="dismissible"
            variant="ghost"
            size="sm"
            @click="dismiss"
            :class="outOfStockCount > 0 ? 'text-red-600 hover:bg-red-100' : 'text-yellow-600 hover:bg-yellow-100'"
          >
            <X class="h-3 w-3" />
          </Button>
        </div>
      </div>
    </AlertDescription>
  </Alert>
</template>
