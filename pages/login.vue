<script setup lang="ts">
import { onKeyStroke } from '@vueuse/core'
import { useForm } from '~/composables/useForm'
import { email, required } from '~/utils/validationRules'

definePageMeta({
  layout: 'auth',
})

const { login, tryLogin } = useAuth()
await tryLogin()

const { notifySuccess } = useToast()

const loading = ref(false)
const { form, validate, errors } = useForm({
  email: {
    default: '',
    rules: [required, email],
  },
  password: {
    default: '',
    rules: [required],
  },
})

async function onSubmit() {
  if (loading.value || !validate())
    return

  loading.value = true
  const success = await login(form)
  loading.value = false
  if (success) {
    notifySuccess('Login successful')
    navigateTo('/')
  }
}

onKeyStroke(['Enter'], (e) => {
  e.preventDefault()
  onSubmit()
})
</script>

<template>
  <BaseCard class="w-full max-w-sm">
    <BaseCardHeader class="relative mb-2">
      <BaseCardTitle class="text-3xl py-2">
        DEZUS
      </BaseCardTitle>
      <BaseCardDescription> Admin dashboard </BaseCardDescription>
    </BaseCardHeader>
    <BaseCardContent class="grid gap-4">
      <FormInput
        id="email"
        v-model="form.email"
        label="Email"
        type="email"
        placeholder="<EMAIL>"
        required
        :error="errors.email"
      />
      <FormInputPassword
        id="password"
        v-model="form.password"
        label="Password"
        type="password"
        required
        :error="errors.password"
      />
    </BaseCardContent>
    <BaseCardFooter>
      <BaseButton
        :disabled="loading"
        class="w-full"
        @click="onSubmit"
        @keyup.enter="onSubmit"
      >
        {{ loading ? 'Loading...' : 'Login' }}
      </BaseButton>
    </BaseCardFooter>
  </BaseCard>
</template>
