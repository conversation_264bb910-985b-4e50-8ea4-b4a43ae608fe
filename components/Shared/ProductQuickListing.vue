<script setup lang="ts">
import { debounce } from '@/utils'
import { Loader, Search } from 'lucide-vue-next'

defineProps<{
  pagination: Pagination<ProductListForCollection[]> | null
  searchQuery: string
  isLoading: boolean
  loadingAction: number[]
  disabledProductIds: number[]
}>()

const emit = defineEmits<{
  (e: 'update:searchQuery', query: string): void
  (e: 'update:page', page: number): void
  (e: 'actionClick', product: ProductListForCollection): void
}>()

function onActionClick(product: ProductListForCollection) {
  emit('actionClick', product)
}

function changePage(page: number) {
  emit('update:page', page)
}

function onSearchInput(event: Event) {
  emit('update:searchQuery', (event.target as HTMLInputElement).value)
}

const onSearchInputDebounced = debounce(onSearchInput, 1500)
</script>

<template>
  <div class="bg-muted p-4 rounded-lg border">
    <div class="mb-4">
      <div class="relative">
        <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <BaseInput
          :value="searchQuery"
          placeholder="Search by name or SKU"
          class="pl-9 w-full"
          @input="onSearchInputDebounced"
        />
      </div>
    </div>

    <!-- Products List -->
    <div class="h-[400px] overflow-y-auto border rounded-md">
      <div v-if="isLoading" class="p-4 text-center text-muted-foreground">
        Loading...
      </div>
      <div v-else-if="!pagination || pagination.data.length === 0" class="p-4 text-center text-muted-foreground">
        No products found
      </div>
      <ul v-else class="divide-y">
        <li
          v-for="product in pagination.data"
          :key="product.id"
          class="hover:bg-accent/50 transition-colors flex items-center justify-between"
          :class="{ 'opacity-50': disabledProductIds?.includes(product.id) }"
        >
          <div class="flex items-center space-x-3 flex-1">
            <div class="w-10 h-12 bg-muted overflow-hidden">
              <img
                v-if="product.image?.path"
                :src="fileUrl(product.image?.path)"
                class="w-full h-full object-cover"
                alt=""
              >
            </div>
            <div class="flex-1">
              <NuxtLink :to="`/products/edit/${product.id}`" class="font-medium text-sm truncate">
                {{ product.name }}
              </NuxtLink>
              <p class="text-xs text-muted-foreground">
                SKU: {{ product.sku }}
              </p>
            </div>
          </div>
          <BaseButton
            size="sm"
            variant="ghost"
            class="rounded-full h-8 w-8 p-0 shrink-0"
            @click="onActionClick(product)"
          >
            <Loader v-if="loadingAction.includes(product.id)" class="h-4 w-4 animate-spin" />
            <slot v-else name="action" />
          </BaseButton>
        </li>
      </ul>
    </div>

    <!-- Pagination -->
    <div v-if="pagination?.total && pagination.total > 0" class="mt-4">
      <div class="flex items-center justify-between">
        <div class="text-sm text-muted-foreground">
          Showing {{ pagination.data.length }} of {{ pagination.total }} products
        </div>
        <div class="flex items-center space-x-2">
          <BaseButton
            size="sm"
            variant="outline"
            :disabled="pagination.current_page === 1"
            @click="changePage(pagination.current_page - 1)"
          >
            Previous
          </BaseButton>
          <BaseButton
            size="sm"
            variant="outline"
            :disabled="pagination.current_page === pagination.last_page"
            @click="changePage(pagination.current_page + 1)"
          >
            Next
          </BaseButton>
        </div>
      </div>
    </div>
  </div>
</template>
