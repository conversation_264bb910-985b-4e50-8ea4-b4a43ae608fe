<script lang="ts" setup>
import { useVModel } from '@vueuse/core'
import { Trash2 } from 'lucide-vue-next'
import { useUpload } from '~/composables/useUpload'

const props = defineProps<{
  modelValue: FileForm | null
  error?: string
}>()
const emit = defineEmits(['update:modelValue'])
const { uploadTemp } = useUpload()
const { notifyError } = useToast()

const image = useVModel(props, 'modelValue', emit)

const loading = ref(false)
async function previewImage(files: FileList) {
  if (loading.value)
    return

  const file = files[0]
  const type = getFileType(file)

  if (!type) {
    notifyError('File type is not supported')
    return
  }

  loading.value = true
  const data = await uploadTemp(files)
  loading.value = false
  if (data && data.length > 0) {
    image.value = {
      url: data[0],
      type: type as FileType,
      id: null,
    }
  }
}

function removeImage() {
  image.value = null
}
</script>

<template>
  <div class="space-y-4">
    <div v-if="error" class="text-sm text-red-500">
      {{ error }}
    </div>

    <div v-if="!image">
      <FormUpload
        title="Upload Image"
        accept="image/*"
        :multiple="false"
        @on-file-upload="previewImage"
      />
    </div>

    <div v-else class="relative w-fit">
      <FilePreview :file="image" />
      <BaseButton
        variant="danger"
        size="xs"
        class="absolute -top-2 -right-2"
        @click="removeImage"
      >
        <Trash2 class="h-3 w-3" />
      </BaseButton>
    </div>
  </div>
</template>
