<script lang="ts" setup>
import type { Order } from '~/types/order'

definePageMeta({
  middleware: ['auth'],
})

const tableSchema = [
  {
    key: 'id',
    label: 'Order ID',
  },
  {
    key: 'customer',
    label: 'Customer',
  },
  {
    key: 'products',
    label: 'Products',
  },
  {
    key: 'total_price',
    label: 'Total Price',
  },
  {
    key: 'discount',
    label: 'Discount',
  },
  {
    key: 'status',
    label: 'Status',
  },
  {
    key: 'created_at',
    label: 'Created At',
  },
]

const { getDefaultHeaders } = useCustomFetch()
const route = useRoute()

const status = computed(() => route.query.status || null)
const keyword = computed(() => route.query.keyword || null)
const page = computed(() => Number(route.query.page || 1))
const perPage = computed(() => Number(route.query.per_page || 15))

const { data: pagination } = await useAsyncData(
  'orders',
  async () => {
    const { data } = await $fetch<ResponseData<Pagination<Order[]>>>('/api/admin/orders', {
      headers: getDefaultHeaders(),
      params: {
        page: page.value || undefined,
        per_page: perPage.value || undefined,
        status: status.value || undefined,
        keyword: keyword.value || undefined,
      },
    })

    return data
  },
  {
    watch: [page, perPage, status, keyword],
  },
)

const orders = computed<Order[]>(() => pagination.value?.data || [])
</script>

<template>
  <BaseCard class="mt-3">
    <BaseCardHeader>
      <BaseCardTitle>Orders</BaseCardTitle>
      <div class="flex mt-5 mb-3 gap-4">
        <SearchWithQuery placeholder="Search by customer email..." />
        <div class="w-44">
          <TableFilter
            param-key="status"
            label="Status"
            placeholder="Select a status"
            :options="[
              { label: 'All', value: 'all' },
              { label: 'Draft', value: 'draft' },
              { label: 'Pending', value: 'pending' },
              { label: 'Completed', value: 'completed' },
            ]"
          />
        </div>
      </div>
    </BaseCardHeader>
    <BaseCardContent>
      <TableTemplate :columns="tableSchema" :data="orders">
        <template #id="{ item }">
          <NuxtLink :to="`/orders/${item.access_token}`">
            Order {{ item.id }}
          </NuxtLink>
        </template>
        <template #products="{ item }">
          <div class="flex flex-col gap-2">
            <OrderProductItem
              v-for="product in item.order_products"
              :key="product.id"
              :product="product"
              :quantity="product.quantity"
            />
          </div>
        </template>
        <template #customer="{ item }">
          <div class="flex flex-col">
            <div v-if="item.customer?.first_name || item.customer?.last_name" class="text-gray-500 text-sm">
              {{ item.customer?.first_name }} {{ item.customer?.last_name }}
            </div>
            <div v-if="item.customer?.email" class="text-gray-500 text-sm">
              {{ item.customer?.email }}
            </div>
            <div v-if="item.customer?.phone" class="text-gray-500 text-sm">
              {{ item.customer?.phone }}
            </div>
            <div v-if="item.customer?.address" class="text-gray-500 text-sm">
              {{ item.customer?.address }}
            </div>
            <div v-if="item.customer?.city || item.customer?.state || item.customer?.zip" class="text-gray-500 text-sm">
              <span v-if="item.customer?.city">
                {{ item.customer?.city }},
              </span>
              <span v-if="item.customer?.state">
                {{ item.customer?.state }},
              </span>
              <span v-if="item.customer?.zip">{{ item.customer?.zip }}</span>
            </div>
            <div v-if="item.customer?.country" class="text-gray-500 text-sm">
              {{ item.customer?.country }}
            </div>
          </div>
        </template>
        <template #status="{ item }">
          <BaseBadge variant="secondary">
            {{ item.status }}
          </BaseBadge>
        </template>
        <template #total_price="{ item }">
          <div class="flex flex-col">
            <div class="font-semibold">
              {{ formatPriceByCurrency(Number(item.total_price), item.currency) }}
            </div>
          </div>
        </template>
        <template #discount="{ item }">
          <div v-if="Number(item.discount_price) > 0" class="flex flex-col">
            <div class="font-semibold text-green-600">
              -{{ formatPriceByCurrency(Number(item.discount_price), item.currency) }}
            </div>
            <div v-if="item.order_products.some(p => p.sale_percent)" class="text-xs text-muted-foreground">
              Up to {{ Math.max(...item.order_products.filter(p => p.sale_percent).map(p => Number(p.sale_percent))) }}% off
            </div>
          </div>
          <div v-else class="text-muted-foreground text-sm">
            No discount
          </div>
        </template>
        <template #order_products_count="{ item }">
          {{ item.order_products.length }}
        </template>
        <template #created_at="{ item }">
          {{ formatRelativeDate(item.created_at) }}
        </template>
      </TableTemplate>

      <!-- Pagination -->
      <div v-if="pagination && pagination.total > 0" class="mt-6">
        <TablePagination
          :total="pagination?.total || 0"
          :current="page"
        />
      </div>
    </BaseCardContent>
  </BaseCard>
</template>
