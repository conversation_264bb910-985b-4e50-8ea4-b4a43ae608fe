<script setup lang="ts">
import { cn } from '@/lib/utils'

interface Props {
  size: 'sm' | 'md' | 'lg' | 'xl'
  color: 'primary' | 'secondary' | 'accent' | 'white'
  className: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  color: 'primary',
  className: '',
})

const sizeClasses = {
  sm: 'h-4 w-4 border-2',
  md: 'h-8 w-8 border-3',
  lg: 'h-12 w-12 border-4',
  xl: 'h-16 w-16 border-4',
}

// Color classes mapping
const colorClasses = {
  primary: 'border-primary/30 border-t-primary',
  secondary: 'border-secondary/30 border-t-secondary',
  accent: 'border-accent/30 border-t-accent',
  white: 'border-white/30 border-t-white',
}

// Computed property for dynamic classes
const computedClasses = computed(() => {
  return cn(
    'animate-spin rounded-full border-solid',
    sizeClasses[props.size],
    colorClasses[props.color],
    props.className,
  )
})
</script>

<template>
  <div
    :class="computedClasses"
  />
</template>
