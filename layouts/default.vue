<script setup lang=ts>
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Calendar, ChevronRight, ChevronsUpDown, Database, FileText, LibraryBig, LogOut, Settings2, Truck, User, UserRoundPen } from 'lucide-vue-next'

const authStore = useAuthStore()

const data = {
  user: {
    name: 'Admin',
    email: authStore.user?.email,
    avatar: '/avatars/shadcn.jpg',
  },
}

const nav = [
  {
    title: 'Products',
    icon: Database,
    url: '/products',
  },
  {
    title: 'Orders',
    icon: Truck,
    url: '/orders?status=pending',
  },
  {
    title: 'Customers',
    icon: User,
    url: '/customers',
  },
  {
    title: 'Collections',
    icon: LibraryBig,
    url: '/collections',
  },
  {
    title: 'Blogs',
    icon: FileText,
    url: '/blogs',
  },
  {
    title: 'Sale Events',
    icon: Calendar,
    url: '/events',
  },
  {
    title: 'Event Pages',
    icon: Calendar,
    url: '/event-page',
  },
  {
    title: 'Settings',
    icon: Settings2,
    child: [
      {
        title: 'Navigation',
        url: '/menus',
      },
      {
        title: 'Banner',
        url: '/banners',
      },
      {
        title: 'Size Guides',
        url: '/size-guides',
      },
      {
        title: 'Colors',
        url: '/colors',
      },
      {
        title: 'Other',
        url: '/other',
      },
    ],
    isActive: false,
  },
]

const openCookie = useCookie('openMenu')
const open = ref(Boolean(openCookie.value ?? true))
watch(open, (value) => {
  openCookie.value = value.toString()
})
</script>

<template>
  <BaseSidebarProvider v-model:open="open">
    <BaseSidebar collapsible="icon">
      <BaseSidebarHeader>
        <BaseSidebarMenu>
          <BaseSidebarMenuItem>
            <BaseSidebarTrigger class="-ml-1" />
          </BaseSidebarMenuItem>
        </BaseSidebarMenu>
      </BaseSidebarHeader>
      <BaseSidebarContent>
        <BaseSidebarGroup>
          <BaseSidebarMenu>
            <template v-for="item in nav" :key="item.title">
              <BaseSidebarMenuItem v-if="!item.child">
                <BaseSidebarMenuButton as-child>
                  <NuxtLink :to="item.url">
                    <component :is="item.icon" />
                    <span>{{ item.title }}</span>
                  </NuxtLink>
                </BaseSidebarMenuButton>
              </BaseSidebarMenuItem>
              <template v-else>
                <BaseCollapsible
                  as-child
                  :default-open="item.isActive"
                  class="group/collapsible"
                >
                  <BaseSidebarMenuItem>
                    <BaseCollapsibleTrigger as-child @click="!open ? open = true : ''">
                      <BaseSidebarMenuButton :tooltip="item.title">
                        <component :is="item.icon" />
                        <span>{{ item.title }}</span>
                        <ChevronRight class="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                      </BaseSidebarMenuButton>
                    </BaseCollapsibleTrigger>
                    <BaseCollapsibleContent>
                      <BaseSidebarMenuSub>
                        <BaseSidebarMenuSubItem
                          v-for="subItem in item.child"
                          :key="subItem.title"
                        >
                          <BaseSidebarMenuSubButton as-child>
                            <NuxtLink :to="subItem.url">
                              <span>{{ subItem.title }}</span>
                            </NuxtLink>
                          </BaseSidebarMenuSubButton>
                        </BaseSidebarMenuSubItem>
                      </BaseSidebarMenuSub>
                    </BaseCollapsibleContent>
                  </BaseSidebarMenuItem>
                </BaseCollapsible>
              </template>
            </template>
          </BaseSidebarMenu>
        </BaseSidebarGroup>
      </BaseSidebarContent>
      <BaseSidebarFooter>
        <BaseSidebarMenu>
          <BaseSidebarMenuItem>
            <BaseDropdownMenu>
              <BaseDropdownMenuTrigger as-child>
                <BaseSidebarMenuButton
                  size="lg"
                  class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <Avatar class="h-8 w-8 rounded-lg">
                    <AvatarImage :src="data.user.avatar" :alt="data.user.name" />
                    <AvatarFallback class="rounded-lg">
                      CN
                    </AvatarFallback>
                  </Avatar>
                  <div class="grid flex-1 text-left text-sm leading-tight">
                    <span class="truncate font-semibold">{{ data.user.name }}</span>
                    <span class="truncate text-xs">{{ data.user.email }}</span>
                  </div>
                  <ChevronsUpDown class="ml-auto size-4" />
                </BaseSidebarMenuButton>
              </BaseDropdownMenuTrigger>
              <BaseDropdownMenuContent class="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg" side="bottom" align="end" :side-offset="4">
                <BaseDropdownMenuGroup>
                  <BaseDropdownMenuItem>
                    <User />
                    Account
                  </BaseDropdownMenuItem>
                  <BaseDropdownMenuItem>
                    <UserRoundPen />
                    Change password
                  </BaseDropdownMenuItem>
                </BaseDropdownMenuGroup>
                <BaseDropdownMenuSeparator />
                <BaseDropdownMenuItem>
                  <LogOut />
                  Log out
                </BaseDropdownMenuItem>
              </BaseDropdownMenuContent>
            </BaseDropdownMenu>
          </BaseSidebarMenuItem>
        </BaseSidebarMenu>
      </BaseSidebarFooter>
      <BaseSidebarRail />
    </BaseSidebar>
    <BaseSidebarInset class="overflow-auto">
      <div class="flex flex-1 flex-col gap-4 p-4 pt-0">
        <slot />
      </div>
    </BaseSidebarInset>
  </BaseSidebarProvider>
</template>
