<script setup lang="ts">
import { useVModel } from '@vueuse/core'

const props = defineProps<{
  modelValue: string
  options: { label: string, value: string }[]
  isDisabled?: boolean
}>()

const emit = defineEmits(['update:modelValue'])

const currentValue = useVModel(props, 'modelValue', emit)
</script>

<template>
  <BaseRadioGroup v-model="currentValue" :disabled="isDisabled">
    <div v-for="option in options" :key="option.value" class="flex items-center space-x-1">
      <BaseRadioGroupItem :id="option.value" :value="option.value" />
      <BaseLabel :for="option.value">
        {{ option.label }}
      </BaseLabel>
    </div>
  </BaseRadioGroup>
</template>
