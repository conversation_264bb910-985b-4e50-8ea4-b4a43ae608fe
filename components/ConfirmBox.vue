<script lang="ts" setup>
defineProps<{
  onConfirm: () => void
}>()
</script>

<template>
  <ClientOnly>
    <template #default>
      <BaseAlertDialog>
        <BaseAlertDialogTrigger>
          <slot />
        </BaseAlertDialogTrigger>
        <BaseAlertDialogContent>
          <BaseAlertDialogHeader>
            <BaseAlertDialogTitle>Are you absolutely sure?</BaseAlertDialogTitle>
            <BaseAlertDialogDescription>
              This action cannot be undone.
            </BaseAlertDialogDescription>
          </BaseAlertDialogHeader>
          <BaseAlertDialogFooter>
            <BaseAlertDialogCancel>Cancel</BaseAlertDialogCancel>
            <BaseAlertDialogAction @click="onConfirm">
              Continue
            </BaseAlertDialogAction>
          </BaseAlertDialogFooter>
        </BaseAlertDialogContent>
      </BaseAlertDialog>
    </template>
    <template #fallback>
      <slot />
    </template>
  </ClientOnly>
</template>
