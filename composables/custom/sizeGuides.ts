import type { TableForm } from '~/types/size-guide'

export function useSizeGuides() {
  const { form, validate, setForm } = useForm({
    name: {
      default: '',
      rules: [required],
    },
    table: {
      default: initDefaultTable(),
      rules: [],
    },
  })

  return { form, validate, setForm }
}

function initDefaultTable(): TableForm {
  return {
    headers: ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'],
    rows: [
      { id: 1, name: 'EUR', cells: ['34', '36', '38', '40', '42', '44', '46'] },
      { id: 2, name: 'UK', cells: ['6', '8', '10', '12', '14', '16', '18'] },
      { id: 3, name: 'US', cells: ['2', '4', '6', '8', '10', '12', '14'] },
      { id: 4, name: 'Bust', cells: ['24.8', '26', '27.2', '28.3', '29.5', '30.7', '31.9'] },
      { id: 5, name: 'Sleeve Length', cells: ['26.4', '26.8', '27.2', '27.6', '28', '28.3', '28.7'] },
      { id: 6, name: 'Length', cells: ['20.5', '21.1', '21.7', '22.2', '22.8', '23.4', '24'] },
      { id: 7, name: 'Waist', cells: ['21.3', '22', '22.8', '23.6', '24.4', '25.6', '26.8'] },
    ],
  }
}
