<script setup lang="ts">
import type { BlogType } from '~/types/blog'
import { Save } from 'lucide-vue-next'
import { createEmptyLocaleContent, getLocaleContent } from '~/config/locales'

definePageMeta({
  middleware: ['auth'],
})

const route = useRoute()
const router = useRouter()
const id = route.params.id as string

const { getDefaultHeaders, clientFetch } = useCustomFetch()
const { notifyError, notifySuccess } = useToast()

interface Blog {
  id: number
  title: string | LocaleContent  // Support both string and LocaleContent for backward compatibility
  content: string
  status: boolean
  type: string
}

const { data: blog, error } = await useAsyncData(`blog-${id}`, async () => {
  const { data } = await $fetch<ResponseData<Blog>>(`/api/blogs/${id}`, {
    headers: getDefaultHeaders(),
  })
  return data
})

// Redirect if blog doesn't exist
if (!blog.value && !error.value) {
  router.push('/blogs')
}

// Convert title to LocaleContent format for consistency
function normalizeTitle(title: string | LocaleContent | undefined): LocaleContent {
  if (!title) {
    return createEmptyLocaleContent() as LocaleContent
  }

  if (typeof title === 'string') {
    // Convert old string format to LocaleContent format
    const content = createEmptyLocaleContent()
    content.en = title  // Use 'en' as default for backward compatibility
    return content as LocaleContent
  }

  return title
}

const { form, errors, validate } = useForm({
  title: {
    default: normalizeTitle(blog.value?.title),
    rules: [required],
  },
  content: {
    default: blog.value?.content || '',
    rules: [required],
  },
  status: {
    default: blog.value?.status || false,
    rules: [],
  },
  type: {
    default: (blog.value?.type || 'company') as BlogType,
    rules: [required],
  },
})

const isSaving = ref(false)

async function onSave() {
  if (!validate()) {
    notifyError('Please fill in all required fields.')
    return
  }

  isSaving.value = true
  try {
    const success = await clientFetch(`/api/blogs/${id}`, {
      method: 'PUT',
      body: {
        title: form.title,
        content: form.content,
        status: form.status,
        type: form.type,
      },
    })

    if (success) {
      notifySuccess('Blog updated successfully')
      router.push('/blogs')
    }
  }
  catch (error) {
    notifyError('Failed to update blog')
    console.error('Error updating blog:', error)
  }
  finally {
    isSaving.value = false
  }
}
</script>

<template>
  <div class="pt-1">
    <div class="flex items-center justify-between mb-6">
      <GoBack />
      <BaseButton
        :disabled="isSaving"
        size="sm"
        @click="onSave"
      >
        <Save class="h-4 w-4" />
        {{ isSaving ? 'Saving...' : 'Save Blog' }}
      </BaseButton>
    </div>
    <BaseCard>
      <BaseCardHeader>
        <BaseCardTitle>
          Edit Blog: {{ getLocaleContent(typeof blog?.title === 'string' ? { en: blog.title } : (blog?.title || {})) }}
        </BaseCardTitle>
      </BaseCardHeader>
      <BaseCardContent>
        <BlogEditor v-model:form="form" class="mt-4" :error="errors" />
      </BaseCardContent>
    </BaseCard>
  </div>
</template>
