<script setup lang="ts">
const colors = defineModel('colors', {
  type: Array as PropType<string[]>,
  required: true,
})

const open = ref(false)
const colorsDB = computed(() => useSettingsStore().colors)

function toggle(color: string) {
  const index = colors.value.indexOf(color)
  if (index === -1) {
    colors.value.push(color)
  }
  else {
    colors.value.splice(index, 1)
  }
}
</script>

<template>
  <ModalTemplate v-model:open="open">
    <template #trigger>
      <slot />
    </template>
    <template #title>
      <h2 class="text-xl uppercase">
        Colors
      </h2>
    </template>
    <template #footer>
      <BaseButton @click="open = false">
        Close
      </BaseButton>
    </template>
    <div class="grid grid-cols-4 gap-6">
      <ColorCard
        v-for="color in colorsDB"
        :key="color.id"
        :color="color"
        class="h-16 cursor-pointer"
        :class="{
          'border-2 border-green-600': colors.includes(color.name),
          'text-gray-900/30': !colors.includes(color.name),
        }"
        @click="toggle(color.name)"
      />
    </div>
  </ModalTemplate>
</template>
