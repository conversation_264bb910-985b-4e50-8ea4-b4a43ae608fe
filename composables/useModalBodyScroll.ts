/**
 * Composable for managing body scroll when modals are open
 * Prevents background scrolling when modal is visible
 *
 * @param isModalOpen - Reactive boolean indicating if modal is open
 *
 * @example
 * ```ts
 * const isModalOpen = ref(false)
 *
 * // Use the composable
 * useModalBodyScroll(isModalOpen)
 *
 * // When modal opens/closes, body scroll is automatically managed
 * function openModal() {
 *   isModalOpen.value = true // Body scroll will be prevented
 * }
 *
 * function closeModal() {
 *   isModalOpen.value = false // Body scroll will be restored
 * }
 * ```
 */
export function useModalBodyScroll(isModalOpen: Ref<boolean>) {
  // Watch modal state to control body scroll
  watch(isModalOpen, (newValue) => {
    if (import.meta.client) {
      if (newValue) {
        // Prevent body scroll when modal opens
        document.body.style.overflow = 'hidden'
      } else {
        // Restore body scroll when modal closes
        document.body.style.overflow = ''
      }
    }
  })

  // Cleanup on unmount
  onUnmounted(() => {
    if (import.meta.client) {
      document.body.style.overflow = ''
    }
  })
}
