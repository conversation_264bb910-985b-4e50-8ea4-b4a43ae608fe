<script lang="ts" setup>
import type { SizeGuide } from '~/types/size-guide'

definePageMeta({
  middleware: ['auth'],
})

const router = useRouter()
const { clientFetch, getDefaultHeaders } = useCustomFetch()
const { notifySuccess } = useToast()

const { data: sizeGuides, refresh } = await useAsyncData('size-guides', async () => {
  const { data } = await $fetch<ResponseData<SizeGuide[]>>(`/api/size-guides`, {
    headers: getDefaultHeaders(),
  })

  return data
})

async function handleDelete(id: string) {
  const success = await clientFetch(`/api/size-guides/${id}`, {
    method: 'DELETE',
  })

  if (success) {
    notifySuccess('Size guide deleted successfully')
    refresh()
  }
}
</script>

<template>
  <div>
    <div class="flex items-center justify-between mt-2">
      <div class="text-lg uppercase">
        Size Guides
      </div>
      <BaseButton @click="router.push('/size-guides/create')">
        Create Size Guide
      </BaseButton>
    </div>
    <BaseCard class="mt-4">
      <BaseCardHeader>
        Size Guides List
      </BaseCardHeader>
      <BaseCardContent>
        <div class="space-y-4">
          <div v-for="guide in sizeGuides" :key="guide.id" class="border rounded-lg p-4">
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium">
                {{ guide.name }}
              </h3>
              <div class="flex gap-2">
                <BaseButton variant="outline" size="sm" @click="router.push(`/size-guides/${guide.id}`)">
                  Edit
                </BaseButton>
                <BaseButton variant="destructive" size="sm" @click="handleDelete(guide.id)">
                  Delete
                </BaseButton>
              </div>
            </div>
          </div>
        </div>
      </BaseCardContent>
    </BaseCard>
  </div>
</template>
