export interface Event {
  id: number
  title: string
  slug: string
  is_active: boolean
  event_rows: EventRow[]
  created_at: string
  updated_at: string
}

export interface EventRow {
  id: number
  event_id: number
  position: number
  event_images: EventImage[]
  created_at: string
  updated_at: string
}

export interface EventImage {
  id: number
  event_row_id: number
  width: ImageWidth
  align: ImageAlign
  link_url: string | null
  position: number
  image: any | null
  images: any[]
  created_at: string
  updated_at: string
}

export type ImageWidth = 'auto' | 'full'
export type ImageAlign = 'left' | 'center' | 'right'

export interface EventListItem {
  id: number
  title: string
  slug: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface EventFormData {
  title: string
  slug: string
  is_active: boolean
  event_rows: EventRowFormData[]
}

export interface EventRowFormData {
  id?: number
  position: number
  event_images: EventImageFormData[]
  _destroy?: boolean
}

export interface EventImageFormData {
  id?: number
  width: ImageWidth
  align: ImageAlign
  link_url: string | null
  position: number
  file: FileForm | null
  _destroy?: boolean
}

// Sale Event Types
export interface SaleEvent {
  id: number
  name: string
  start_date: string
  end_date: string
  sale_percent: number
  status: boolean
  description?: string
  created_at: string
  updated_at: string
  is_active: boolean
  is_upcoming: boolean
  is_expired: boolean
  products_count?: number
  products?: ProductListForSaleEvent[]
  statistics?: SaleEventStatistics
}

export interface SaleEventStatistics {
  total_products: number
  ordered_products: number
  total_revenue: number
  can_delete: boolean
}

export interface SaleEventListItem {
  id: number
  name: string
  start_date: string
  end_date: string
  sale_percent: number
  status: boolean
  description?: string
  created_at: string
  updated_at: string
  is_active: boolean
  is_upcoming: boolean
  is_expired: boolean
  products_count: number
}

export interface SaleEventFormData {
  name: string
  start_date: string
  end_date: string
  sale_percent: number
  status: boolean
  description?: string
}

export interface ProductListForSaleEvent {
  id: number
  name: string
  slug: string
  sku: string
  status?: boolean
  sale_id?: number
  price?: number
  original_price?: number
  sale_price?: number
  sale_percent?: string
  has_sale?: boolean
  currency?: string
  prices_by_location?: PriceByLocation[]
  sale_event?: SaleEvent
  image?: FileDB
}

export interface PriceByLocation {
  location_id: number
  location: string
  price: number
  compare_price?: number | null
}
