<script setup lang="ts">
import { type DialogRootEmits, type DialogRootProps, useForwardPropsEmits } from 'reka-ui'

const props = defineProps<DialogRootProps>()
const emits = defineEmits<DialogRootEmits>()

const forwarded = useForwardPropsEmits(props, emits)
</script>

<template>
  <BaseDialog v-bind="forwarded">
    <BaseDialogTrigger as-child>
      <button>
        <slot name="trigger" />
      </button>
    </BaseDialogTrigger>
    <BaseDialogContent class="modal-container grid-rows-[auto_minmax(0,1fr)_auto] p-0 max-h-[90dvh]">
      <BaseDialogHeader class="py-2 px-4 pb-0">
        <BaseDialogTitle>
          <slot name="title" />
        </BaseDialogTitle>
        <BaseDialogDescription>
          <slot name="description" />
        </BaseDialogDescription>
      </BaseDialogHeader>
      <div class="grid gap-4 py-4 overflow-y-auto px-6">
        <slot />
      </div>
      <BaseDialogFooter class="py-2 px-4 pt-0">
        <slot name="footer" />
      </BaseDialogFooter>
    </BaseDialogContent>
  </BaseDialog>
</template>
