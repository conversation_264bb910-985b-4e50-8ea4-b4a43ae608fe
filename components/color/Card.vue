<script setup lang="ts">
defineProps<{
  color: Option
}>()
</script>

<template>
  <div class="flex rounded overflow-hidden shadow-md select-none">
    <div
      :style="{
        backgroundColor: color.value,
      }"
      class="w-12 border-r border-gray-200"
    />
    <div class="text-center flex-1 my-auto">
      <div class="capitalize">
        {{ color.name }}
      </div>
      <div>{{ color.value }}</div>
    </div>
  </div>
</template>
