import { cva, type VariantProps } from 'class-variance-authority'

export { default as <PERSON><PERSON> } from './Button.vue'

export const buttonVariants = cva(
  'inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground shadow hover:bg-primary/90',
        destructive:
          'bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90',
        outline:
          'border border-input bg-background hover:bg-accent shadow-sm hover:text-accent-foreground',
        outlinePrimary:
          'border border-primary bg-background shadow-sm hover:bg-primary hover:text-primary-foreground',
        outlineDanger:
          'border border-red-600 text-red-600 shadow-sm bg-background hover:bg-red-600 hover:text-white',
        secondary:
          'bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80',
        success: 'shadow bg-green-700 text-white shadow hover:bg-green-700/90',
        outlineSuccess:
          'shadow-sm border border-green-700 text-green-700 bg-background hover:bg-green-700 hover:text-white',
        danger: 'shadow bg-red-700 text-white hover:bg-red-700/90',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
      },
      size: {
        default: 'h-9 px-4 py-2',
        xs: 'h-7 rounded px-2',
        sm: 'h-8 rounded-md px-3 text-xs',
        lg: 'h-10 rounded-md px-8',
        icon: 'h-9 w-9',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
)

export type ButtonVariants = VariantProps<typeof buttonVariants>
