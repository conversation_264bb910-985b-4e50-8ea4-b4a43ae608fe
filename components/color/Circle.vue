<script setup lang="ts">
defineOptions({
  inheritAttrs: false,
})
const props = defineProps<{
  color: string
}>()

const settingsStore = useSettingsStore()
const colors = computed(() => settingsStore.colors || [])
const colorDB = computed(() => colors.value.find(c => c.name === props.color))
</script>

<template>
  <div
    v-if="colorDB"
    v-bind="$attrs"
    class="border border-gray-200 rounded-full inline-flex items-center justify-center"
    :style="{ backgroundColor: colorDB.value }"
  />
</template>
