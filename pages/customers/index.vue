<script lang="ts" setup>
definePageMeta({
  middleware: ['auth'],
})

const tableSchema = [
  {
    key: 'id',
    label: 'Customer ID',
  },
  {
    key: 'name',
    label: 'Name',
  },
  {
    key: 'email',
    label: 'Email',
  },
  {
    key: 'contact',
    label: 'Contact',
  },
  {
    key: 'orders',
    label: 'Orders',
  },
  {
    key: 'email_verified',
    label: 'Email Verified',
  },
  {
    key: 'created_at',
    label: 'Created At',
  },
]

const { getDefaultHeaders } = useCustomFetch()
const route = useRoute()

const keyword = computed(() => route.query.keyword || null)
const verified = computed(() => route.query.verified || null)
const page = computed(() => Number(route.query.page || 1))
const perPage = computed(() => Number(route.query.per_page || 15))

const { data: pagination } = await useAsyncData(
  'customers',
  async () => {
    const { data } = await $fetch<ResponseData<Pagination<Customer[]>>>('/api/customers', {
      headers: getDefaultHeaders(),
      params: {
        page: page.value || undefined,
        per_page: perPage.value || undefined,
        verified: verified.value || undefined,
        keyword: keyword.value || undefined,
      },
    })

    return data
  },
  {
    watch: [page, perPage, verified, keyword],
  },
)

const customers = computed<Customer[]>(() => pagination.value?.data || [])
</script>

<template>
  <BaseCard class="mt-3">
    <BaseCardHeader>
      <BaseCardTitle>Customers</BaseCardTitle>
      <div class="flex mt-5 mb-3 gap-4">
        <SearchWithQuery placeholder="Search by name or email..." />
        <div class="w-44">
          <TableFilter
            param-key="verified"
            label="Email Status"
            placeholder="Select status"
            :options="[
              { label: 'All', value: 'all' },
              { label: 'Verified', value: 'verified' },
              { label: 'Unverified', value: 'unverified' },
            ]"
          />
        </div>
      </div>
    </BaseCardHeader>
    <BaseCardContent>
      <TableTemplate :columns="tableSchema" :data="customers">
        <template #id="{ item }">
          Customer {{ item.id }}
        </template>
        <template #name="{ item }">
          <div class="flex flex-col">
            <div v-if="item.first_name || item.last_name" class="font-medium">
              {{ item.first_name }} {{ item.last_name }}
            </div>
            <div v-else class="text-gray-400 italic">
              No name provided
            </div>
          </div>
        </template>
        <template #email="{ item }">
          <div class="flex flex-col">
            <div class="text-sm">
              {{ item.email }}
            </div>
          </div>
        </template>
        <template #contact="{ item }">
          <div class="flex flex-col gap-1">
            <div v-if="item.phone" class="text-sm text-gray-600">
              📞 {{ item.phone }}
            </div>
            <div v-if="item.address" class="text-sm text-gray-600">
              📍 {{ item.address }}
            </div>
            <div v-if="item.city || item.state || item.zip" class="text-sm text-gray-600">
              <span v-if="item.city">{{ item.city }}</span><span v-if="item.city && (item.state || item.zip)">, </span>
              <span v-if="item.state">{{ item.state }}</span><span v-if="item.state && item.zip" />
              <span v-if="item.zip">{{ item.zip }}</span>
            </div>
            <div v-if="item.country" class="text-sm text-gray-600">
              {{ item.country }}
            </div>
          </div>
        </template>
        <template #orders="{ item }">
          <div class="flex flex-col gap-1">
            <div v-if="item.non_draft_orders_count" class="text-sm">
              <BaseBadge variant="default" class="text-xs">
                {{ item.non_draft_orders_count }} Completed
              </BaseBadge>
            </div>
            <div v-if="item.draft_orders_count" class="text-sm">
              <BaseBadge variant="secondary" class="text-xs">
                {{ item.draft_orders_count }} Draft
              </BaseBadge>
            </div>
            <div v-if="!item.non_draft_orders_count && !item.draft_orders_count" class="text-gray-400 italic text-sm">
              No orders
            </div>
          </div>
        </template>
        <template #email_verified="{ item }">
          <BaseBadge :variant="item.email_verified_at ? 'default' : 'secondary'">
            {{ item.email_verified_at ? 'Verified' : 'Unverified' }}
          </BaseBadge>
        </template>
        <template #created_at="{ item }">
          {{ formatRelativeDate(item.created_at) }}
        </template>
      </TableTemplate>

      <!-- Pagination -->
      <div v-if="pagination && pagination.total > 0" class="mt-6">
        <TablePagination
          :total="pagination?.total || 0"
          :current="page"
        />
      </div>
    </BaseCardContent>
  </BaseCard>
</template>
