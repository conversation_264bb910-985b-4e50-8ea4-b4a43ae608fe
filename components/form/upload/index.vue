<script setup lang="ts">
import { CloudUpload } from 'lucide-vue-next'

defineProps({
  title: {
    type: String,
    default: 'Upload',
  },
  accept: {
    type: String,
    default: 'image/*',
  },
  multiple: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['onFileUpload'])

function onFileChange(event: Event) {
  const target = event.target as HTMLInputElement
  if (!target.files?.length)
    return
  emit('onFileUpload', target.files)
  setTimeout(() => {
    target.value = ''
  }, 1000)
}
const fileInput = ref<HTMLInputElement | null>(null)
function onFileClick() {
  fileInput.value?.click()
}
</script>

<template>
  <div class="cursor-pointer group w-fit" @click="onFileClick">
    <BaseButton size="sm" variant="success" class="flex items-center">
      <CloudUpload name="icons:upload" class="w-5 h-5 text-white" />
      <div>
        {{ title }}
      </div>
    </BaseButton>
    <input
      ref="fileInput"
      class="hidden"
      type="file"
      :multiple="multiple"
      :accept="accept"
      @change="onFileChange"
    >
  </div>
</template>
