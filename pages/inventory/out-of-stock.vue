<script setup lang="ts">
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Button } from '~/components/ui/button'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'
import { Alert, AlertDescription } from '~/components/ui/alert'
import { Badge } from '~/components/ui/badge'
import StockStatus from '~/components/Inventory/StockStatus.vue'
import StockManager from '~/components/Inventory/StockManager.vue'
import { TrendingDown, RefreshCw, AlertCircle, Package } from 'lucide-vue-next'

definePageMeta({
  layout: 'default'
})

const { getDefaultHeaders } = useCustomFetch()
const { notifyError } = useToast()

// Reactive data
const loading = ref(false)
const outOfStockVariants = ref<Variant[]>([])

// Methods
async function fetchOutOfStockVariants() {
  try {
    loading.value = true
    
    const response = await $fetch('/api/v1/admin/variants/out-of-stock', {
      headers: getDefaultHeaders()
    })
    
    outOfStockVariants.value = response.data || []
  } catch (error) {
    console.error('Failed to fetch out of stock variants:', error)
    notifyError('Failed to load out of stock variants')
  } finally {
    loading.value = false
  }
}

function onStockUpdated(updatedVariant: Variant) {
  const index = outOfStockVariants.value.findIndex(v => v.id === updatedVariant.id)
  if (index !== -1) {
    // If stock is now above 0, remove from list
    if ((updatedVariant.quantity || 0) > 0) {
      outOfStockVariants.value.splice(index, 1)
    } else {
      outOfStockVariants.value[index] = updatedVariant
    }
  }
}

// Lifecycle
onMounted(() => {
  fetchOutOfStockVariants()
})
</script>

<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold tracking-tight flex items-center gap-2">
          <TrendingDown class="h-8 w-8 text-red-600" />
          Out of Stock Variants
        </h1>
        <p class="text-muted-foreground">
          Variants that require immediate restocking
        </p>
      </div>
      <Button @click="fetchOutOfStockVariants" :disabled="loading">
        <RefreshCw class="h-4 w-4 mr-2" :class="{ 'animate-spin': loading }" />
        Refresh
      </Button>
    </div>

    <!-- Critical Alert if variants found -->
    <Alert v-if="outOfStockVariants.length > 0" class="border-red-200 bg-red-50">
      <AlertCircle class="h-4 w-4 text-red-600" />
      <AlertDescription class="text-red-800">
        <strong>Critical:</strong> {{ outOfStockVariants.length }} variant{{ outOfStockVariants.length > 1 ? 's are' : ' is' }} 
        completely out of stock and unavailable for sale. Immediate restocking required.
      </AlertDescription>
    </Alert>

    <!-- Out of Stock Variants Table -->
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <TrendingDown class="h-5 w-5 text-red-600" />
          Out of Stock Variants ({{ outOfStockVariants.length }})
        </CardTitle>
        <CardDescription>
          Variants with zero stock that need immediate attention
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div v-if="loading" class="flex items-center justify-center py-8">
          <RefreshCw class="h-6 w-6 animate-spin mr-2" />
          Loading out of stock variants...
        </div>
        
        <div v-else-if="outOfStockVariants.length === 0" class="text-center py-8">
          <Package class="h-12 w-12 text-green-600 mx-auto mb-4" />
          <h3 class="text-lg font-semibold text-green-600 mb-2">Excellent!</h3>
          <p class="text-muted-foreground">
            No variants are currently out of stock. All variants have inventory available.
          </p>
        </div>
        
        <Table v-else>
          <TableHeader>
            <TableRow>
              <TableHead>SKU</TableHead>
              <TableHead>Size</TableHead>
              <TableHead>SAPO SKU</TableHead>
              <TableHead class="text-center">Stock Status</TableHead>
              <TableHead class="text-center">Priority</TableHead>
              <TableHead class="text-center">Last Updated</TableHead>
              <TableHead class="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow v-for="variant in outOfStockVariants" :key="variant.id" class="border-l-4 border-l-red-500">
              <TableCell class="font-medium">{{ variant.sku }}</TableCell>
              <TableCell>{{ variant.size }}</TableCell>
              <TableCell>{{ variant.sapo_sku || '-' }}</TableCell>
              <TableCell class="text-center">
                <StockStatus :quantity="0" />
              </TableCell>
              <TableCell class="text-center">
                <Badge variant="destructive" class="bg-red-600 hover:bg-red-600">
                  <AlertCircle class="h-3 w-3 mr-1" />
                  Critical
                </Badge>
              </TableCell>
              <TableCell class="text-center text-sm text-muted-foreground">
                <!-- This would show when the variant was last updated -->
                Recently
              </TableCell>
              <TableCell class="text-right">
                <StockManager :variant="variant" @stock-updated="onStockUpdated" />
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>

    <!-- Impact Analysis -->
    <Card v-if="outOfStockVariants.length > 0">
      <CardHeader>
        <CardTitle class="flex items-center gap-2">
          <AlertCircle class="h-5 w-5 text-red-600" />
          Impact Analysis
        </CardTitle>
        <CardDescription>Understanding the business impact of out-of-stock variants</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid gap-4 md:grid-cols-3">
          <div class="space-y-2">
            <div class="text-2xl font-bold text-red-600">{{ outOfStockVariants.length }}</div>
            <div class="text-sm text-muted-foreground">Variants Out of Stock</div>
          </div>
          <div class="space-y-2">
            <div class="text-2xl font-bold text-orange-600">0</div>
            <div class="text-sm text-muted-foreground">Potential Lost Sales</div>
          </div>
          <div class="space-y-2">
            <div class="text-2xl font-bold text-blue-600">0</div>
            <div class="text-sm text-muted-foreground">Affected Products</div>
          </div>
        </div>
        
        <div class="mt-4 p-4 bg-red-50 rounded-lg border border-red-200">
          <h4 class="font-semibold text-red-800 mb-2">Recommended Actions:</h4>
          <ul class="text-sm text-red-700 space-y-1">
            <li>• Prioritize restocking high-demand variants</li>
            <li>• Contact suppliers for expedited delivery</li>
            <li>• Consider temporary product substitutions</li>
            <li>• Update product availability on website</li>
          </ul>
        </div>
      </CardContent>
    </Card>

    <!-- Quick Actions -->
    <Card v-if="outOfStockVariants.length > 0">
      <CardHeader>
        <CardTitle>Emergency Actions</CardTitle>
        <CardDescription>Quick operations for out-of-stock variants</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="flex gap-2">
          <Button variant="destructive" disabled>
            <AlertCircle class="h-4 w-4 mr-2" />
            Generate Restock Report
          </Button>
          <Button variant="outline" disabled>
            <TrendingDown class="h-4 w-4 mr-2" />
            Notify Suppliers
          </Button>
          <Button variant="outline" disabled>
            <Package class="h-4 w-4 mr-2" />
            Bulk Emergency Restock
          </Button>
        </div>
        <p class="text-sm text-muted-foreground mt-2">
          Emergency bulk operations coming soon. Use individual stock managers for immediate restocking.
        </p>
      </CardContent>
    </Card>
  </div>
</template>
