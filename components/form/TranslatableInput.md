# FormTranslatableInput Component

A beautiful, reusable component for handling translatable content with modern UX design.

## Features

✨ **Modern Design**: Card-based layout with clean visual hierarchy
🌍 **Multi-language Support**: Automatic support for all configured locales
📊 **Progress Tracking**: Visual progress indicators and translation status
🎯 **Smart Validation**: Context-aware validation and error handling
🔄 **Real-time Feedback**: Instant visual feedback for translation status
⚡ **Type Safe**: Full TypeScript support with proper typing

## Basic Usage

```vue
<template>
  <FormTranslatableInput
    v-model="form.title"
    label="Product Name"
    :required="true"
    placeholder="Enter a descriptive product name..."
  />
</template>

<script setup lang="ts">
const form = reactive({
  title: createEmptyLocaleContent() as LocaleContent
})
</script>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `modelValue` | `LocaleContent` | - | **Required.** The locale content object |
| `label` | `string` | - | **Required.** Field label displayed in header |
| `placeholder` | `string` | `''` | Custom placeholder text |
| `required` | `boolean` | `false` | Whether the default locale is required |
| `disabled` | `boolean` | `false` | Disable all inputs |
| `error` | `any` | - | Error object for validation display |
| `helpText` | `string` | - | Custom help text (overrides default contextual help) |
| `type` | `'input' \| 'textarea'` | `'input'` | Input field type |
| `rows` | `number` | `3` | Number of rows for textarea |

## Examples

### 1. Basic Input Field
```vue
<FormTranslatableInput
  v-model="product.name"
  label="Product Name"
  :required="true"
  placeholder="Enter product name..."
/>
```

### 2. Textarea Field
```vue
<FormTranslatableInput
  v-model="product.description"
  label="Product Description"
  type="textarea"
  :rows="5"
  placeholder="Describe your product..."
/>
```

### 3. Optional Field
```vue
<FormTranslatableInput
  v-model="product.subtitle"
  label="Product Subtitle"
  :required="false"
  placeholder="Optional marketing subtitle..."
/>
```

### 4. With Custom Help Text
```vue
<FormTranslatableInput
  v-model="meta.keywords"
  label="SEO Keywords"
  help-text="Enter comma-separated keywords for search optimization"
  placeholder="keyword1, keyword2, keyword3..."
/>
```

### 5. With Error Handling
```vue
<FormTranslatableInput
  v-model="form.title"
  label="Blog Title"
  :required="true"
  :error="errors.title"
  :disabled="isSubmitting"
/>
```

## Complete Form Example

```vue
<template>
  <form @submit.prevent="onSubmit" class="space-y-6">
    <!-- Product Name (Required) -->
    <FormTranslatableInput
      v-model="form.name"
      label="Product Name"
      :required="true"
      :error="errors.name"
      :disabled="isSubmitting"
      placeholder="Enter a compelling product name..."
    />

    <!-- Product Description (Optional) -->
    <FormTranslatableInput
      v-model="form.description"
      label="Product Description"
      type="textarea"
      :rows="4"
      :error="errors.description"
      :disabled="isSubmitting"
      placeholder="Describe your product features and benefits..."
    />

    <!-- SEO Meta Description (Optional) -->
    <FormTranslatableInput
      v-model="form.metaDescription"
      label="Meta Description"
      type="textarea"
      :rows="2"
      help-text="This will be used for search engine results (150-160 characters recommended)"
      placeholder="Brief description for search engines..."
    />

    <div class="flex justify-end gap-3">
      <BaseButton type="button" variant="outline" @click="onCancel">
        Cancel
      </BaseButton>
      <BaseButton type="submit" :disabled="isSubmitting">
        {{ isSubmitting ? 'Saving...' : 'Save Product' }}
      </BaseButton>
    </div>
  </form>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { createEmptyLocaleContent } from '~/config/locales'

const form = reactive({
  name: createEmptyLocaleContent() as LocaleContent,
  description: createEmptyLocaleContent() as LocaleContent,
  metaDescription: createEmptyLocaleContent() as LocaleContent,
})

const errors = ref({})
const isSubmitting = ref(false)

const { validate } = useForm({
  name: {
    default: form.name,
    rules: [required]
  },
  description: {
    default: form.description,
    rules: []
  },
  metaDescription: {
    default: form.metaDescription,
    rules: []
  }
})

async function onSubmit() {
  if (!validate()) {
    return
  }

  isSubmitting.value = true
  try {
    await submitForm(form)
    // Handle success
  } catch (error) {
    // Handle error
  } finally {
    isSubmitting.value = false
  }
}
</script>
```

## Advanced Features

### Translation Status
The component automatically shows:
- **Progress bar** indicating completion percentage
- **Status icons** for each locale (✓ filled, ⚠ required missing, ○ empty)
- **Tab indicators** showing which locales have content
- **Contextual help** explaining the current field's purpose

### Visual States
- **Default state**: Clean, minimal appearance
- **Active state**: Blue highlight for selected locale tab
- **Filled state**: Green accents for completed translations
- **Required missing**: Amber warning for required but empty default locale
- **Error state**: Red styling for validation errors

### Smart Fallbacks
- Non-default locales show fallback information
- Empty optional fields explain they'll use the default locale
- Contextual help adapts based on current state

## Styling

The component uses a card-based design with:
- Clean borders and subtle shadows
- Consistent spacing and typography
- Smooth transitions for interactive elements
- Color-coded status indicators
- Responsive layout that works on all screen sizes

## Best Practices

1. **Always provide meaningful labels** that clearly describe the content
2. **Use appropriate field types** (input vs textarea) based on content length
3. **Add custom help text** for complex or specialized fields
4. **Handle validation errors** properly with the error prop
5. **Consider user workflow** when deciding which fields are required
6. **Test with multiple locales** to ensure proper display

## Migration from Old Pattern

**Before (manual implementation):**
```vue
<!-- Old, complex manual implementation -->
<div class="space-y-3">
  <FormLocaleSelector v-model="currentLocale" />
  <FormInput v-model="form.title[currentLocale]" />
  <div class="flex gap-2">
    <span v-for="locale in locales">{{ locale }}: ✓</span>
  </div>
</div>
```

**After (new component):**
```vue
<!-- New, clean component usage -->
<FormTranslatableInput
  v-model="form.title"
  label="Title"
  :required="true"
/>
```

## Benefits

- **90% less code** for translatable fields
- **Consistent UX** across the entire application
- **Better accessibility** with proper labels and ARIA attributes
- **Enhanced visual feedback** for better user experience
- **Type safety** with full TypeScript support
- **Future-proof** - automatically supports new locales when added
