<script setup lang="ts">
import { useVModel } from '@vueuse/core'

const props = defineProps<{
  modelValue: string | undefined | number
  options: {
    label: string
    value: string
  }[]
  csPlaceholder: string
  csClass?: string
}>()
const emit = defineEmits(['update:modelValue'])
const current = useVModel(props, 'modelValue', emit)
</script>

<template>
  <BaseSelect v-model="current">
    <BaseSelectTrigger :aria-label="csPlaceholder" :class="csClass">
      <BaseSelectValue :placeholder="csPlaceholder" />
    </BaseSelectTrigger>
    <BaseSelectContent>
      <BaseSelectItem
        v-for="option in options"
        :key="option.value"
        :value="option.value"
      >
        {{ option.label }}
      </BaseSelectItem>
    </BaseSelectContent>
  </BaseSelect>
</template>
