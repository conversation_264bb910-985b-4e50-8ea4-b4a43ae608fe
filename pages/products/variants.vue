<script setup lang="ts">
// const variantTable = [
//   {
//     key: 'color',
//     label: 'Color',
//   },
//   {
//     key: 'size',
//     label: 'Size',
//   },
//   {
//     key: 'sku',
//     label: 'SKU',
//   },
//   {
//     key: 'adjust_price',
//     label: 'Adjust Price',
//   },
// ]

// const variants = computed(() => {
//   return form.options.colors.flatMap((color) => {
//     return form.options.sizes.map((size) => {
//       const sku = `${form.sku}-${color}-${size}`
//       return {
//         color,
//         size,
//         adjust_price: 0,
//         sku,
//         id: sku,
//       }
//     })
//   })
// })

// const search = ref('')
// const filteredVariants = computed(() => {
//   return variants.value.filter((variant) => {
//     return variant.sku.includes(search.value)
//   })
// })

// function updateAdjustPrice(variant: Variant, value: number | null) {
//   const index = form.variants.findIndex(v => v.sku === variant.sku)
//   if (!index) {
//     variant.adjust_price = value ?? 0
//     form.variants.push(variant)
//   }
//   else {
//     form.variants[index].adjust_price = value ?? 0
//   }
// }
</script>

<template>
  In development
  <!-- <BaseCard class="col-span-4">
    <BaseCardHeader class="flex justify-between">
      <div>Variants</div>
      <SearchOnly
        v-model="search"
        placeholder="Search variants"
      />
    </BaseCardHeader>
    <BaseCardContent>
      <TableTemplate
        :columns="variantTable"
        :data="filteredVariants"
        :max-height="400"
      >
        <template #color="{ item }">
          <ColorCircle :color="item.color" class="w-8 h-8" />
        </template>
        <template #size="{ item }">
          <span>{{ item.size }}</span>
        </template>
        <template #sku="{ item }">
          <span>{{ item.sku }}</span>
        </template>
        <template #adjust_price="{ item }">
          <FormInputMoney
            :id="`adjust_price.${item.id}`"
            :model-value="item.adjust_price"
            @update:model-value="updateAdjustPrice(item, $event)"
          />
        </template>
      </TableTemplate>
    </BaseCardContent>
  </BaseCard> -->
</template>
